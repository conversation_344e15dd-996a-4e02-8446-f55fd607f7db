# C/C++ build system timings
generate_cxx_metadata
  [gap of 108ms]
  create-invalidation-state 170ms
  generate-prefab-packages
    [gap of 285ms]
    exec-prefab 33945ms
    [gap of 282ms]
  generate-prefab-packages completed in 34512ms
  execute-generate-process
    exec-configure 9325ms
    [gap of 1941ms]
  execute-generate-process completed in 11272ms
  [gap of 610ms]
  write-metadata-json-to-file 28ms
  [gap of 15ms]
generate_cxx_metadata completed in 46745ms

