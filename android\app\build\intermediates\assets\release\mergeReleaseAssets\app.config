{"name": "quiz-bee-techs", "slug": "snack-97152fc1-f368-437d-ac54-171df4ba22bd", "version": "1.3.0", "orientation": "portrait", "icon": "./assets/images/beetech_icon.png", "userInterfaceStyle": "light", "newArchEnabled": true, "splash": {"image": "./assets/images/splash-icon.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "ios": {"supportsTablet": true, "infoPlist": {"NSMicrophoneUsageDescription": "This app needs access to microphone for voice input in AI chat.", "NSSpeechRecognitionUsageDescription": "This app needs access to speech recognition for voice input in AI chat."}}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/beetech_icon.png", "backgroundColor": "#ffffff"}, "package": "com.gokul719.snack97152fc1f368437dac54171df4ba22bd", "versionCode": 5, "minSdkVersion": 21, "permissions": ["CAMERA", "RECORD_AUDIO", "android.permission.RECORD_AUDIO", "android.permission.VIBRATE", "android.permission.WAKE_LOCK", "com.google.android.gms.permission.AD_ID"]}, "web": {"favicon": "./assets/images/favicon.png"}, "description": "neet app", "extra": {"eas": {"projectId": "d2a60988-caa0-4cab-8b31-e41df67d0e29"}}, "react-native-google-mobile-ads": {"androidAppId": "ca-app-pub-9706687137550019~9208363455", "iosAppId": "ca-app-pub-9706687137550019~9208363455"}, "plugins": [["expo-build-properties", {"android": {"compileSdkVersion": 35, "targetSdkVersion": 34, "buildToolsVersion": "35.0.0", "kotlinVersion": "1.9.25"}, "ios": {"deploymentTarget": "15.1"}}], "withAdIdPermission", ["react-native-google-mobile-ads", {"androidAppId": "ca-app-pub-9706687137550019~9208363455", "iosAppId": "ca-app-pub-9706687137550019~9208363455"}]], "sdkVersion": "52.0.0", "platforms": ["ios", "android", "web"]}