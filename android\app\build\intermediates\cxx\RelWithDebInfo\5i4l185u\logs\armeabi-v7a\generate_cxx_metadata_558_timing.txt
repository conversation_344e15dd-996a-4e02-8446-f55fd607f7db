# C/C++ build system timings
generate_cxx_metadata
  [gap of 140ms]
  create-invalidation-state 166ms
  generate-prefab-packages
    [gap of 490ms]
    exec-prefab 18665ms
    [gap of 411ms]
  generate-prefab-packages completed in 19566ms
  execute-generate-process
    [gap of 22ms]
    exec-configure 23407ms
    [gap of 493ms]
  execute-generate-process completed in 23922ms
  [gap of 265ms]
  write-metadata-json-to-file 11ms
generate_cxx_metadata completed in 44102ms

