import React, { useState, useEffect, useRef } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ScrollView, Alert, Animated } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';

import { useRoute, useNavigation } from '@react-navigation/native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import BannerAdComponent from '../components/BannerAdComponent';
import { AD_PLACEMENTS } from '../src/config/adConfig';

const MockExamsScreen = () => {
  const darkMode = false;
  const fontSizeMultiplier = 1;
  const route = useRoute();
  const navigation = useNavigation();
  const { category = 'NEET' } = route.params || {};

  const [examHistory, setExamHistory] = useState([]);
  const [selectedExamType, setSelectedExamType] = useState('NEET');
  const fadeAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    loadExamHistory();
    Animated.timing(fadeAnim, {
      toValue: 1,
      duration: 800,
      useNativeDriver: true,
    }).start();
  }, []);

  const examTypes = [
    {
      id: 'NEET',
      name: 'NEET Mock Exam',
      duration: 180, // 3 hours
      questions: 180,
      subjects: ['Physics', 'Chemistry', 'Biology'],
      description: 'Full NEET pattern exam with 180 questions in 3 hours',
      colors: ['#667eea', '#764ba2']
    },
    {
      id: 'JEE_MAIN',
      name: 'JEE Main Mock',
      duration: 120, // 2 hours (reduced since Mathematics not available)
      questions: 60, // Reduced from 90 since only 2 subjects available
      subjects: ['Physics', 'Chemistry'],
      description: 'JEE Main pattern with Physics & Chemistry (60 questions in 2 hours)',
      colors: ['#f093fb', '#f5576c']
    },
    {
      id: 'PHYSICS_ONLY',
      name: 'Physics Mock',
      duration: 60, // 1 hour
      questions: 45,
      subjects: ['Physics'],
      description: 'Physics-only mock exam for focused practice',
      colors: ['#4ECDC4', '#44A08D']
    },
    {
      id: 'CHEMISTRY_ONLY',
      name: 'Chemistry Mock',
      duration: 60, // 1 hour
      questions: 45,
      subjects: ['Chemistry'],
      description: 'Chemistry-only mock exam for focused practice',
      colors: ['#FFA726', '#FF7043']
    },
    {
      id: 'BIOLOGY_ONLY',
      name: 'Biology Mock',
      duration: 60, // 1 hour
      questions: 45,
      subjects: ['Biology'],
      description: 'Biology-only mock exam for focused practice',
      colors: ['#AB47BC', '#8E24AA']
    }
  ];

  const loadExamHistory = async () => {
    try {
      const history = await AsyncStorage.getItem(`mock_exam_history_${category}`);
      if (history) {
        setExamHistory(JSON.parse(history));
      }
    } catch (error) {
      console.error('Error loading exam history:', error);
    }
  };

  const saveExamResult = async (result) => {
    try {
      const newHistory = [result, ...examHistory.slice(0, 9)]; // Keep last 10 exams
      setExamHistory(newHistory);
      await AsyncStorage.setItem(
        `mock_exam_history_${category}`,
        JSON.stringify(newHistory)
      );
    } catch (error) {
      console.error('Error saving exam result:', error);
    }
  };

  const startMockExam = (examType) => {
    Alert.alert(
      'Start Mock Exam',
      `You are about to start ${examType.name}.\n\n` +
      `Duration: ${examType.duration} minutes\n` +
      `Questions: ${examType.questions}\n` +
      `Subjects: ${examType.subjects.join(', ')}\n\n` +
      `This will simulate real exam conditions with strict timing.`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Start Exam',
          onPress: () => {
            navigation.navigate('TimedQuiz', {
              category: examType.id,
              duration: examType.duration * 60, // Convert to seconds
              questionCount: examType.questions,
              testType: 'mock_exam',
              examType: examType,
              onTestComplete: saveExamResult,
              strictMode: true // Enable strict exam mode
            });
          }
        }
      ]
    );
  };

  const formatDate = (timestamp) => {
    const date = new Date(timestamp);
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  const getScoreColor = (percentage) => {
    if (percentage >= 80) return '#4CAF50';
    if (percentage >= 60) return '#FF9800';
    return '#F44336';
  };

  const getGrade = (percentage) => {
    if (percentage >= 90) return 'A+';
    if (percentage >= 80) return 'A';
    if (percentage >= 70) return 'B+';
    if (percentage >= 60) return 'B';
    if (percentage >= 50) return 'C';
    return 'F';
  };

  const renderExamCard = (examType, index) => (
    <Animated.View
      key={examType.id}
      style={[
        styles.examCard,
        {
          opacity: fadeAnim,
          transform: [{
            translateY: fadeAnim.interpolate({
              inputRange: [0, 1],
              outputRange: [50, 0]
            })
          }]
        }
      ]}
    >
      <TouchableOpacity
        onPress={() => startMockExam(examType)}
        style={styles.examButton}
        activeOpacity={0.8}
      >
        <LinearGradient
          colors={examType.colors}
          style={styles.examGradient}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
        >
          <View style={styles.examHeader}>
            <Text style={[
              styles.examTitle,
              { fontSize: 18 * fontSizeMultiplier }
            ]}>
              {examType.name}
            </Text>
            <Ionicons name="school-outline" size={24} color="#FFFFFF" />
          </View>

          <Text style={[
            styles.examDescription,
            { fontSize: 14 * fontSizeMultiplier }
          ]}>
            {examType.description}
          </Text>

          <View style={styles.examDetails}>
            <View style={styles.examDetailItem}>
              <Ionicons name="time-outline" size={16} color="#FFFFFF" />
              <Text style={[
                styles.examDetailText,
                { fontSize: 12 * fontSizeMultiplier }
              ]}>
                {examType.duration} min
              </Text>
            </View>

            <View style={styles.examDetailItem}>
              <Ionicons name="help-circle-outline" size={16} color="#FFFFFF" />
              <Text style={[
                styles.examDetailText,
                { fontSize: 12 * fontSizeMultiplier }
              ]}>
                {examType.questions} questions
              </Text>
            </View>
          </View>

          <View style={styles.startExamButton}>
            <Text style={[
              styles.startExamText,
              { fontSize: 14 * fontSizeMultiplier }
            ]}>
              Start Exam
            </Text>
            <Ionicons name="play" size={16} color="#FFFFFF" />
          </View>
        </LinearGradient>
      </TouchableOpacity>
    </Animated.View>
  );

  const renderExamHistory = () => {
    if (examHistory.length === 0) {
      return (
        <View style={styles.emptyHistory}>
          <Ionicons name="document-text-outline" size={48} color="#CCCCCC" />
          <Text style={[
            styles.emptyHistoryText,
            {
              color: darkMode ? '#CCCCCC' : '#666666',
              fontSize: 16 * fontSizeMultiplier
            }
          ]}>
            No mock exams completed yet
          </Text>
        </View>
      );
    }

    return examHistory.map((exam, index) => (
      <Animated.View
        key={index}
        style={[
          styles.historyItem,
          {
            backgroundColor: darkMode ? '#2A2A2A' : '#FFFFFF',
            opacity: fadeAnim
          }
        ]}
      >
        <View style={styles.historyHeader}>
          <View style={styles.historyTitleContainer}>
            <Text style={[
              styles.historyTitle,
              {
                color: darkMode ? '#FFFFFF' : '#333333',
                fontSize: 16 * fontSizeMultiplier
              }
            ]}>
              {exam.examType?.name || 'Mock Exam'}
            </Text>
            <View style={[
              styles.gradeContainer,
              { backgroundColor: getScoreColor(exam.percentage) }
            ]}>
              <Text style={[
                styles.gradeText,
                { fontSize: 12 * fontSizeMultiplier }
              ]}>
                {getGrade(exam.percentage)}
              </Text>
            </View>
          </View>
          <Text style={[
            styles.historyDate,
            {
              color: darkMode ? '#CCCCCC' : '#666666',
              fontSize: 12 * fontSizeMultiplier
            }
          ]}>
            {formatDate(exam.timestamp)}
          </Text>
        </View>

        <View style={styles.historyStats}>
          <View style={styles.statItem}>
            <Text style={[
              styles.statValue,
              {
                color: getScoreColor(exam.percentage),
                fontSize: 20 * fontSizeMultiplier
              }
            ]}>
              {exam.percentage}%
            </Text>
            <Text style={[
              styles.statLabel,
              {
                color: darkMode ? '#CCCCCC' : '#666666',
                fontSize: 12 * fontSizeMultiplier
              }
            ]}>
              Score
            </Text>
          </View>

          <View style={styles.statItem}>
            <Text style={[
              styles.statValue,
              {
                color: darkMode ? '#FFFFFF' : '#333333',
                fontSize: 20 * fontSizeMultiplier
              }
            ]}>
              {exam.correct}/{exam.total}
            </Text>
            <Text style={[
              styles.statLabel,
              {
                color: darkMode ? '#CCCCCC' : '#666666',
                fontSize: 12 * fontSizeMultiplier
              }
            ]}>
              Correct
            </Text>
          </View>

          <View style={styles.statItem}>
            <Text style={[
              styles.statValue,
              {
                color: darkMode ? '#FFFFFF' : '#333333',
                fontSize: 20 * fontSizeMultiplier
              }
            ]}>
              {Math.floor(exam.timeSpent / 60)}m
            </Text>
            <Text style={[
              styles.statLabel,
              {
                color: darkMode ? '#CCCCCC' : '#666666',
                fontSize: 12 * fontSizeMultiplier
              }
            ]}>
              Time Used
            </Text>
          </View>
        </View>
      </Animated.View>
    ));
  };

  return (
    <View style={[styles.container, darkMode ? styles.darkContainer : styles.lightContainer]}>
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {/* Header */}
        <View style={styles.header}>
          <Text style={[
            styles.headerTitle,
            {
              color: darkMode ? '#FFFFFF' : '#333333',
              fontSize: 24 * fontSizeMultiplier
            }
          ]}>
            Mock Exams
          </Text>
          <Text style={[
            styles.headerSubtitle,
            {
              color: darkMode ? '#CCCCCC' : '#666666',
              fontSize: 16 * fontSizeMultiplier
            }
          ]}>
            Full-length mock examinations for comprehensive practice
          </Text>
        </View>

        {/* Available Exams */}
        <View style={styles.examsSection}>
          <Text style={[
            styles.sectionTitle,
            {
              color: darkMode ? '#FFFFFF' : '#333333',
              fontSize: 18 * fontSizeMultiplier
            }
          ]}>
            Available Mock Exams
          </Text>

          {examTypes.map((examType, index) => renderExamCard(examType, index))}
        </View>

        {/* Exam History */}
        <View style={styles.historySection}>
          <Text style={[
            styles.sectionTitle,
            {
              color: darkMode ? '#FFFFFF' : '#333333',
              fontSize: 18 * fontSizeMultiplier
            }
          ]}>
            Recent Mock Exams
          </Text>
          {renderExamHistory()}
        </View>
      </ScrollView>

      {/* Banner Ad */}
      <View style={styles.adContainer}>
        <BannerAdComponent
          placement={AD_PLACEMENTS.SETTINGS_BANNER}
          fallbackToWebView={true}
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  darkContainer: {
    backgroundColor: '#121212',
  },
  lightContainer: {
    backgroundColor: '#F5F5F5',
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 20,
  },
  header: {
    padding: 20,
    paddingTop: 40,
    alignItems: 'center',
  },
  headerTitle: {
    fontWeight: 'bold',
    marginBottom: 8,
  },
  headerSubtitle: {
    textAlign: 'center',
    lineHeight: 22,
  },
  examsSection: {
    paddingHorizontal: 20,
    marginBottom: 30,
  },
  sectionTitle: {
    fontWeight: 'bold',
    marginBottom: 16,
  },
  examCard: {
    marginBottom: 16,
    borderRadius: 16,
    overflow: 'hidden',
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
  },
  examButton: {
    width: '100%',
  },
  examGradient: {
    padding: 20,
  },
  examHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  examTitle: {
    color: '#FFFFFF',
    fontWeight: 'bold',
    flex: 1,
  },
  examDescription: {
    color: '#FFFFFF',
    opacity: 0.9,
    marginBottom: 16,
    lineHeight: 20,
  },
  examDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  examDetailItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  examDetailText: {
    color: '#FFFFFF',
    marginLeft: 4,
    opacity: 0.9,
  },
  startExamButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 20,
    alignSelf: 'center',
  },
  startExamText: {
    color: '#FFFFFF',
    fontWeight: '500',
    marginRight: 4,
  },
  historySection: {
    paddingHorizontal: 20,
  },
  historyItem: {
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
  },
  historyHeader: {
    marginBottom: 12,
  },
  historyTitleContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  historyTitle: {
    fontWeight: 'bold',
    flex: 1,
  },
  gradeContainer: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  gradeText: {
    color: '#FFFFFF',
    fontWeight: 'bold',
  },
  historyDate: {
    fontStyle: 'italic',
  },
  historyStats: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  statItem: {
    alignItems: 'center',
  },
  statValue: {
    fontWeight: 'bold',
    marginBottom: 4,
  },
  statLabel: {
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },
  emptyHistory: {
    alignItems: 'center',
    paddingVertical: 40,
  },
  emptyHistoryText: {
    marginTop: 16,
    textAlign: 'center',
  },
  adContainer: {
    width: '100%',
    alignItems: 'center',
    backgroundColor: 'transparent',
  },
});

export default MockExamsScreen;
