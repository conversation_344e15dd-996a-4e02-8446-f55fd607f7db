import React, { useState, useEffect, useRef } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ScrollView, Animated, Alert } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';

import { useRoute, useNavigation } from '@react-navigation/native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import BannerAdComponent from '../components/BannerAdComponent';
import { AD_PLACEMENTS } from '../src/config/adConfig';

const AdaptiveLearningScreen = () => {
  const darkMode = false;
  const fontSizeMultiplier = 1;
  const route = useRoute();
  const navigation = useNavigation();
  const { category = 'NEET' } = route.params || {};

  const [userProfile, setUserProfile] = useState(null);
  const [recommendations, setRecommendations] = useState([]);
  const [weakAreas, setWeakAreas] = useState([]);
  const [studyStreak, setStudyStreak] = useState(0);
  const [loading, setLoading] = useState(true);
  const fadeAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    loadUserProfile();
    generateRecommendations();
    Animated.timing(fadeAnim, {
      toValue: 1,
      duration: 1000,
      useNativeDriver: true,
    }).start();
  }, []);

  const loadUserProfile = async () => {
    try {
      const profile = await AsyncStorage.getItem(`user_profile_${category}`);
      const streak = await AsyncStorage.getItem(`study_streak_${category}`);

      if (profile) {
        setUserProfile(JSON.parse(profile));
      } else {
        // Create default profile
        const defaultProfile = {
          totalQuestions: 0,
          correctAnswers: 0,
          subjectPerformance: {
            Physics: { correct: 0, total: 0, lastStudied: null },
            Chemistry: { correct: 0, total: 0, lastStudied: null },
            Biology: { correct: 0, total: 0, lastStudied: null },
            Mathematics: { correct: 0, total: 0, lastStudied: null }
          },
          difficultyPerformance: {
            easy: { correct: 0, total: 0 },
            medium: { correct: 0, total: 0 },
            hard: { correct: 0, total: 0 }
          },
          studyTime: 0,
          lastActive: Date.now()
        };
        setUserProfile(defaultProfile);
        await AsyncStorage.setItem(`user_profile_${category}`, JSON.stringify(defaultProfile));
      }

      if (streak) {
        setStudyStreak(parseInt(streak));
      }
    } catch (error) {
      console.error('Error loading user profile:', error);
    } finally {
      setLoading(false);
    }
  };

  const generateRecommendations = async () => {
    try {
      // Analyze user performance and generate AI-powered recommendations
      const profile = await AsyncStorage.getItem(`user_profile_${category}`);
      if (!profile) return;

      const userData = JSON.parse(profile);
      const newRecommendations = [];
      const newWeakAreas = [];

      // Analyze subject performance
      Object.entries(userData.subjectPerformance || {}).forEach(([subject, performance]) => {
        const accuracy = performance.total > 0 ? (performance.correct / performance.total) * 100 : 0;

        if (accuracy < 60 && performance.total > 5) {
          newWeakAreas.push({
            subject,
            accuracy: accuracy.toFixed(1),
            questionsAttempted: performance.total,
            priority: accuracy < 40 ? 'high' : 'medium'
          });

          newRecommendations.push({
            id: `weak_${subject}`,
            type: 'improvement',
            title: `Focus on ${subject}`,
            description: `Your accuracy in ${subject} is ${accuracy.toFixed(1)}%. Practice more questions to improve.`,
            action: 'Practice',
            subject: subject,
            priority: accuracy < 40 ? 'high' : 'medium',
            icon: 'trending-up-outline',
            colors: ['#FF6B6B', '#FF8E8E']
          });
        } else if (accuracy > 80 && performance.total > 10) {
          newRecommendations.push({
            id: `strong_${subject}`,
            type: 'maintenance',
            title: `Maintain ${subject} strength`,
            description: `Great job! Keep practicing ${subject} to maintain your ${accuracy.toFixed(1)}% accuracy.`,
            action: 'Review',
            subject: subject,
            priority: 'low',
            icon: 'checkmark-circle-outline',
            colors: ['#4ECDC4', '#44A08D']
          });
        }
      });

      // Analyze difficulty performance
      Object.entries(userData.difficultyPerformance || {}).forEach(([difficulty, performance]) => {
        const accuracy = performance.total > 0 ? (performance.correct / performance.total) * 100 : 0;

        if (accuracy < 50 && performance.total > 5) {
          newRecommendations.push({
            id: `difficulty_${difficulty}`,
            type: 'skill_building',
            title: `Work on ${difficulty} questions`,
            description: `Your accuracy on ${difficulty} questions is ${accuracy.toFixed(1)}%. Focus on building these skills.`,
            action: 'Practice',
            difficulty: difficulty,
            priority: 'medium',
            icon: 'barbell-outline',
            colors: ['#FFA726', '#FF7043']
          });
        }
      });

      // Time-based recommendations
      const daysSinceLastActive = Math.floor((Date.now() - userData.lastActive) / (1000 * 60 * 60 * 24));
      if (daysSinceLastActive > 2) {
        newRecommendations.push({
          id: 'comeback',
          type: 'motivation',
          title: 'Welcome back!',
          description: `It's been ${daysSinceLastActive} days since your last study session. Let's get back on track!`,
          action: 'Start Quiz',
          priority: 'high',
          icon: 'rocket-outline',
          colors: ['#667eea', '#764ba2']
        });
      }

      // Study streak recommendations
      if (studyStreak > 0) {
        newRecommendations.push({
          id: 'streak',
          type: 'motivation',
          title: `${studyStreak} day streak!`,
          description: 'Keep up the great work! Consistency is key to success.',
          action: 'Continue',
          priority: 'low',
          icon: 'flame-outline',
          colors: ['#f093fb', '#f5576c']
        });
      }

      // Default recommendation if no specific ones
      if (newRecommendations.length === 0) {
        newRecommendations.push({
          id: 'general',
          type: 'general',
          title: 'Start your learning journey',
          description: 'Take a quiz to help us understand your strengths and create personalized recommendations.',
          action: 'Take Quiz',
          priority: 'medium',
          icon: 'school-outline',
          colors: ['#AB47BC', '#8E24AA']
        });
      }

      setRecommendations(newRecommendations.sort((a, b) => {
        const priorityOrder = { high: 3, medium: 2, low: 1 };
        return priorityOrder[b.priority] - priorityOrder[a.priority];
      }));

      setWeakAreas(newWeakAreas);
    } catch (error) {
      console.error('Error generating recommendations:', error);
    }
  };

  const handleRecommendationAction = (recommendation) => {
    switch (recommendation.action) {
      case 'Practice':
        if (recommendation.subject) {
          navigation.navigate('MainApp', {
            screen: 'Quiz',
            params: {
              category: recommendation.subject,
              difficulty: recommendation.difficulty || 'medium'
            }
          });
        } else if (recommendation.difficulty) {
          navigation.navigate('MainApp', {
            screen: 'Quiz',
            params: {
              category: 'NEET',
              difficulty: recommendation.difficulty
            }
          });
        }
        break;
      case 'Review':
        navigation.navigate('Flashcards', {
          category: recommendation.subject || 'NEET'
        });
        break;
      case 'Start Quiz':
      case 'Take Quiz':
        navigation.navigate('MainApp', {
          screen: 'Quiz',
          params: { category: 'NEET' }
        });
        break;
      case 'Continue':
        navigation.navigate('MainApp', {
          screen: 'Quiz',
          params: { category: 'NEET' }
        });
        break;
      default:
        Alert.alert('Coming Soon', 'This feature will be available soon!');
    }
  };

  const getPriorityColor = (priority) => {
    switch (priority) {
      case 'high': return '#F44336';
      case 'medium': return '#FF9800';
      case 'low': return '#4CAF50';
      default: return '#2196F3';
    }
  };

  const renderRecommendation = (recommendation, index) => (
    <Animated.View
      key={recommendation.id}
      style={[
        styles.recommendationCard,
        {
          opacity: fadeAnim,
          transform: [{
            translateY: fadeAnim.interpolate({
              inputRange: [0, 1],
              outputRange: [30, 0]
            })
          }]
        }
      ]}
    >
      <TouchableOpacity
        onPress={() => handleRecommendationAction(recommendation)}
        style={styles.recommendationButton}
        activeOpacity={0.8}
      >
        <LinearGradient
          colors={recommendation.colors}
          style={styles.recommendationGradient}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
        >
          <View style={styles.recommendationHeader}>
            <View style={styles.recommendationTitleContainer}>
              <Ionicons name={recommendation.icon} size={24} color="#FFFFFF" />
              <Text style={[
                styles.recommendationTitle,
                { fontSize: 16 * fontSizeMultiplier }
              ]}>
                {recommendation.title}
              </Text>
            </View>
            <View style={[
              styles.priorityBadge,
              { backgroundColor: getPriorityColor(recommendation.priority) }
            ]}>
              <Text style={[
                styles.priorityText,
                { fontSize: 10 * fontSizeMultiplier }
              ]}>
                {recommendation.priority.toUpperCase()}
              </Text>
            </View>
          </View>

          <Text style={[
            styles.recommendationDescription,
            { fontSize: 14 * fontSizeMultiplier }
          ]}>
            {recommendation.description}
          </Text>

          <View style={styles.actionButton}>
            <Text style={[
              styles.actionButtonText,
              { fontSize: 14 * fontSizeMultiplier }
            ]}>
              {recommendation.action}
            </Text>
            <Ionicons name="arrow-forward" size={16} color="#FFFFFF" />
          </View>
        </LinearGradient>
      </TouchableOpacity>
    </Animated.View>
  );

  const renderWeakArea = (area, index) => (
    <View key={index} style={[
      styles.weakAreaItem,
      { backgroundColor: darkMode ? '#2A2A2A' : '#FFFFFF' }
    ]}>
      <View style={styles.weakAreaHeader}>
        <Text style={[
          styles.weakAreaSubject,
          {
            color: darkMode ? '#FFFFFF' : '#333333',
            fontSize: 16 * fontSizeMultiplier
          }
        ]}>
          {area.subject}
        </Text>
        <View style={[
          styles.accuracyBadge,
          { backgroundColor: area.priority === 'high' ? '#F44336' : '#FF9800' }
        ]}>
          <Text style={[
            styles.accuracyText,
            { fontSize: 12 * fontSizeMultiplier }
          ]}>
            {area.accuracy}%
          </Text>
        </View>
      </View>
      <Text style={[
        styles.weakAreaDetails,
        {
          color: darkMode ? '#CCCCCC' : '#666666',
          fontSize: 12 * fontSizeMultiplier
        }
      ]}>
        {area.questionsAttempted} questions attempted • {area.priority} priority
      </Text>
    </View>
  );

  if (loading) {
    return (
      <View style={[styles.container, styles.centerContent]}>
        <Text style={[styles.loadingText, { color: darkMode ? '#FFFFFF' : '#333333' }]}>
          Analyzing your performance...
        </Text>
      </View>
    );
  }

  return (
    <View style={[styles.container, darkMode ? styles.darkContainer : styles.lightContainer]}>
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {/* Header */}
        <View style={styles.header}>
          <Text style={[
            styles.headerTitle,
            {
              color: darkMode ? '#FFFFFF' : '#333333',
              fontSize: 24 * fontSizeMultiplier
            }
          ]}>
            AI Study Plan
          </Text>
          <Text style={[
            styles.headerSubtitle,
            {
              color: darkMode ? '#CCCCCC' : '#666666',
              fontSize: 16 * fontSizeMultiplier
            }
          ]}>
            Personalized recommendations based on your performance
          </Text>
        </View>

        {/* Study Stats */}
        {userProfile && (
          <View style={styles.statsContainer}>
            <View style={[
              styles.statCard,
              { backgroundColor: darkMode ? '#2A2A2A' : '#FFFFFF' }
            ]}>
              <Text style={[
                styles.statValue,
                {
                  color: darkMode ? '#FFFFFF' : '#333333',
                  fontSize: 24 * fontSizeMultiplier
                }
              ]}>
                {userProfile.totalQuestions > 0 ?
                  Math.round((userProfile.correctAnswers / userProfile.totalQuestions) * 100) : 0}%
              </Text>
              <Text style={[
                styles.statLabel,
                {
                  color: darkMode ? '#CCCCCC' : '#666666',
                  fontSize: 12 * fontSizeMultiplier
                }
              ]}>
                Overall Accuracy
              </Text>
            </View>

            <View style={[
              styles.statCard,
              { backgroundColor: darkMode ? '#2A2A2A' : '#FFFFFF' }
            ]}>
              <Text style={[
                styles.statValue,
                {
                  color: darkMode ? '#FFFFFF' : '#333333',
                  fontSize: 24 * fontSizeMultiplier
                }
              ]}>
                {userProfile.totalQuestions}
              </Text>
              <Text style={[
                styles.statLabel,
                {
                  color: darkMode ? '#CCCCCC' : '#666666',
                  fontSize: 12 * fontSizeMultiplier
                }
              ]}>
                Questions Solved
              </Text>
            </View>

            <View style={[
              styles.statCard,
              { backgroundColor: darkMode ? '#2A2A2A' : '#FFFFFF' }
            ]}>
              <Text style={[
                styles.statValue,
                {
                  color: darkMode ? '#FFFFFF' : '#333333',
                  fontSize: 24 * fontSizeMultiplier
                }
              ]}>
                {studyStreak}
              </Text>
              <Text style={[
                styles.statLabel,
                {
                  color: darkMode ? '#CCCCCC' : '#666666',
                  fontSize: 12 * fontSizeMultiplier
                }
              ]}>
                Day Streak
              </Text>
            </View>
          </View>
        )}

        {/* Recommendations */}
        <View style={styles.recommendationsSection}>
          <Text style={[
            styles.sectionTitle,
            {
              color: darkMode ? '#FFFFFF' : '#333333',
              fontSize: 18 * fontSizeMultiplier
            }
          ]}>
            Recommended for You
          </Text>

          {recommendations.map((recommendation, index) => renderRecommendation(recommendation, index))}
        </View>

        {/* Weak Areas */}
        {weakAreas.length > 0 && (
          <View style={styles.weakAreasSection}>
            <Text style={[
              styles.sectionTitle,
              {
                color: darkMode ? '#FFFFFF' : '#333333',
                fontSize: 18 * fontSizeMultiplier
              }
            ]}>
              Areas to Improve
            </Text>

            {weakAreas.map((area, index) => renderWeakArea(area, index))}
          </View>
        )}
      </ScrollView>

      {/* Banner Ad */}
      <View style={styles.adContainer}>
        <BannerAdComponent
          placement={AD_PLACEMENTS.SETTINGS_BANNER}
          fallbackToWebView={true}
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  darkContainer: {
    backgroundColor: '#121212',
  },
  lightContainer: {
    backgroundColor: '#F5F5F5',
  },
  centerContent: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 20,
  },
  header: {
    padding: 20,
    paddingTop: 40,
    alignItems: 'center',
  },
  headerTitle: {
    fontWeight: 'bold',
    marginBottom: 8,
  },
  headerSubtitle: {
    textAlign: 'center',
    lineHeight: 22,
  },
  loadingText: {
    fontSize: 18,
    textAlign: 'center',
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingHorizontal: 20,
    marginBottom: 30,
  },
  statCard: {
    flex: 1,
    padding: 16,
    marginHorizontal: 4,
    borderRadius: 12,
    alignItems: 'center',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
  },
  statValue: {
    fontWeight: 'bold',
    marginBottom: 4,
  },
  statLabel: {
    textAlign: 'center',
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },
  recommendationsSection: {
    paddingHorizontal: 20,
    marginBottom: 30,
  },
  sectionTitle: {
    fontWeight: 'bold',
    marginBottom: 16,
  },
  recommendationCard: {
    marginBottom: 16,
    borderRadius: 16,
    overflow: 'hidden',
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
  },
  recommendationButton: {
    width: '100%',
  },
  recommendationGradient: {
    padding: 20,
  },
  recommendationHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  recommendationTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  recommendationTitle: {
    color: '#FFFFFF',
    fontWeight: 'bold',
    marginLeft: 8,
    flex: 1,
  },
  priorityBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  priorityText: {
    color: '#FFFFFF',
    fontWeight: 'bold',
  },
  recommendationDescription: {
    color: '#FFFFFF',
    opacity: 0.9,
    marginBottom: 16,
    lineHeight: 20,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 20,
    alignSelf: 'flex-start',
  },
  actionButtonText: {
    color: '#FFFFFF',
    fontWeight: '500',
    marginRight: 4,
  },
  weakAreasSection: {
    paddingHorizontal: 20,
  },
  weakAreaItem: {
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
  },
  weakAreaHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  weakAreaSubject: {
    fontWeight: 'bold',
    flex: 1,
  },
  accuracyBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  accuracyText: {
    color: '#FFFFFF',
    fontWeight: 'bold',
  },
  weakAreaDetails: {
    fontStyle: 'italic',
  },
  adContainer: {
    width: '100%',
    alignItems: 'center',
    backgroundColor: 'transparent',
  },
});

export default AdaptiveLearningScreen;
