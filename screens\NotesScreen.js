import React, { useState, useEffect, useRef } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ScrollView, Alert, Animated, TextInput, Modal } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';

import { useNavigation } from '@react-navigation/native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import BannerAdComponent from '../components/BannerAdComponent';
import { AD_PLACEMENTS } from '../src/config/adConfig';

const NotesScreen = () => {
  const darkMode = false;
  const fontSizeMultiplier = 1;
  const navigation = useNavigation();

  const [notes, setNotes] = useState([]);
  const [filteredNotes, setFilteredNotes] = useState([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('All');
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [editingNote, setEditingNote] = useState(null);
  const [newNoteTitle, setNewNoteTitle] = useState('');
  const [newNoteContent, setNewNoteContent] = useState('');
  const [newNoteCategory, setNewNoteCategory] = useState('General');
  const [loading, setLoading] = useState(true);
  const fadeAnim = useRef(new Animated.Value(0)).current;

  const categories = ['All', 'Physics', 'Chemistry', 'Biology', 'Mathematics', 'General'];

  useEffect(() => {
    loadNotes();
    Animated.timing(fadeAnim, {
      toValue: 1,
      duration: 800,
      useNativeDriver: true,
    }).start();
  }, []);

  useEffect(() => {
    filterNotes();
  }, [notes, searchQuery, selectedCategory]);

  const loadNotes = async () => {
    try {
      const notesData = await AsyncStorage.getItem('user_notes');
      if (notesData) {
        const parsedNotes = JSON.parse(notesData);
        setNotes(parsedNotes);
      }
    } catch (error) {
      console.error('Error loading notes:', error);
    } finally {
      setLoading(false);
    }
  };

  const filterNotes = () => {
    let filtered = notes;

    // Filter by category
    if (selectedCategory !== 'All') {
      filtered = filtered.filter(note => note.category === selectedCategory);
    }

    // Filter by search query
    if (searchQuery.trim()) {
      filtered = filtered.filter(note =>
        note.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        note.content.toLowerCase().includes(searchQuery.toLowerCase()) ||
        note.category.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    // Sort by last modified (newest first)
    filtered.sort((a, b) => new Date(b.lastModified) - new Date(a.lastModified));

    setFilteredNotes(filtered);
  };

  const createOrUpdateNote = async () => {
    if (!newNoteTitle.trim()) {
      Alert.alert('Error', 'Please enter a note title');
      return;
    }

    if (!newNoteContent.trim()) {
      Alert.alert('Error', 'Please enter note content');
      return;
    }

    try {
      let updatedNotes;

      if (editingNote) {
        // Update existing note
        updatedNotes = notes.map(note =>
          note.id === editingNote.id
            ? {
                ...note,
                title: newNoteTitle.trim(),
                content: newNoteContent.trim(),
                category: newNoteCategory,
                lastModified: new Date().toISOString()
              }
            : note
        );
      } else {
        // Create new note
        const newNote = {
          id: Date.now().toString(),
          title: newNoteTitle.trim(),
          content: newNoteContent.trim(),
          category: newNoteCategory,
          createdAt: new Date().toISOString(),
          lastModified: new Date().toISOString(),
          highlights: [],
          tags: []
        };
        updatedNotes = [newNote, ...notes];
      }

      setNotes(updatedNotes);
      await AsyncStorage.setItem('user_notes', JSON.stringify(updatedNotes));

      resetModal();
      Alert.alert('Success', editingNote ? 'Note updated successfully!' : 'Note created successfully!');
    } catch (error) {
      console.error('Error saving note:', error);
      Alert.alert('Error', 'Failed to save note');
    }
  };

  const deleteNote = async (noteId) => {
    Alert.alert(
      'Delete Note',
      'Are you sure you want to delete this note? This action cannot be undone.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              const updatedNotes = notes.filter(note => note.id !== noteId);
              setNotes(updatedNotes);
              await AsyncStorage.setItem('user_notes', JSON.stringify(updatedNotes));
            } catch (error) {
              console.error('Error deleting note:', error);
              Alert.alert('Error', 'Failed to delete note');
            }
          }
        }
      ]
    );
  };

  const editNote = (note) => {
    setEditingNote(note);
    setNewNoteTitle(note.title);
    setNewNoteContent(note.content);
    setNewNoteCategory(note.category);
    setShowCreateModal(true);
  };

  const resetModal = () => {
    setShowCreateModal(false);
    setEditingNote(null);
    setNewNoteTitle('');
    setNewNoteContent('');
    setNewNoteCategory('General');
  };

  const getCategoryColor = (category) => {
    const colors = {
      Physics: '#667eea',
      Chemistry: '#f093fb',
      Biology: '#4ECDC4',
      Mathematics: '#FFA726',
      General: '#AB47BC'
    };
    return colors[category] || colors.General;
  };

  const truncateContent = (content, maxLength = 100) => {
    if (content.length <= maxLength) return content;
    return content.substring(0, maxLength) + '...';
  };

  const renderNoteCard = (note, index) => (
    <Animated.View
      key={note.id}
      style={[
        styles.noteCard,
        {
          backgroundColor: darkMode ? '#2A2A2A' : '#FFFFFF',
          opacity: fadeAnim,
          transform: [{
            translateY: fadeAnim.interpolate({
              inputRange: [0, 1],
              outputRange: [30, 0]
            })
          }]
        }
      ]}
    >
      <TouchableOpacity
        onPress={() => navigation.navigate('NoteDetails', { note })}
        style={styles.noteContent}
        activeOpacity={0.7}
      >
        <View style={styles.noteHeader}>
          <View style={styles.noteTitleContainer}>
            <Text style={[
              styles.noteTitle,
              {
                color: darkMode ? '#FFFFFF' : '#333333',
                fontSize: 16 * fontSizeMultiplier
              }
            ]}>
              {note.title}
            </Text>
            <View style={[
              styles.categoryBadge,
              { backgroundColor: getCategoryColor(note.category) }
            ]}>
              <Text style={[
                styles.categoryText,
                { fontSize: 10 * fontSizeMultiplier }
              ]}>
                {note.category}
              </Text>
            </View>
          </View>

          <View style={styles.noteActions}>
            <TouchableOpacity
              style={styles.actionButton}
              onPress={() => editNote(note)}
            >
              <Ionicons name="create-outline" size={20} color="#667eea" />
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.actionButton}
              onPress={() => deleteNote(note.id)}
            >
              <Ionicons name="trash-outline" size={20} color="#F44336" />
            </TouchableOpacity>
          </View>
        </View>

        <Text style={[
          styles.notePreview,
          {
            color: darkMode ? '#CCCCCC' : '#666666',
            fontSize: 14 * fontSizeMultiplier
          }
        ]}>
          {truncateContent(note.content)}
        </Text>

        <View style={styles.noteFooter}>
          <Text style={[
            styles.dateText,
            {
              color: darkMode ? '#AAAAAA' : '#888888',
              fontSize: 12 * fontSizeMultiplier
            }
          ]}>
            {new Date(note.lastModified).toLocaleDateString()} • {new Date(note.lastModified).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
          </Text>

          <View style={styles.noteStats}>
            <Text style={[
              styles.statsText,
              {
                color: darkMode ? '#AAAAAA' : '#888888',
                fontSize: 10 * fontSizeMultiplier
              }
            ]}>
              {note.content.length} chars
            </Text>
          </View>
        </View>
      </TouchableOpacity>
    </Animated.View>
  );

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Ionicons name="document-text-outline" size={64} color="#CCCCCC" />
      <Text style={[
        styles.emptyStateTitle,
        {
          color: darkMode ? '#FFFFFF' : '#333333',
          fontSize: 18 * fontSizeMultiplier
        }
      ]}>
        No Notes Yet
      </Text>
      <Text style={[
        styles.emptyStateText,
        {
          color: darkMode ? '#CCCCCC' : '#666666',
          fontSize: 14 * fontSizeMultiplier
        }
      ]}>
        Create your first note to start organizing your study materials
      </Text>

      <TouchableOpacity
        style={styles.createFirstButton}
        onPress={() => setShowCreateModal(true)}
      >
        <LinearGradient
          colors={['#667eea', '#764ba2']}
          style={styles.createFirstGradient}
        >
          <Ionicons name="add" size={20} color="#FFFFFF" />
          <Text style={[
            styles.createFirstText,
            { fontSize: 14 * fontSizeMultiplier }
          ]}>
            Create Your First Note
          </Text>
        </LinearGradient>
      </TouchableOpacity>
    </View>
  );

  if (loading) {
    return (
      <View style={[styles.container, styles.centerContent]}>
        <Text style={[styles.loadingText, { color: darkMode ? '#FFFFFF' : '#333333' }]}>
          Loading notes...
        </Text>
      </View>
    );
  }

  return (
    <View style={[styles.container, darkMode ? styles.darkContainer : styles.lightContainer]}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={[
          styles.headerTitle,
          {
            color: darkMode ? '#FFFFFF' : '#333333',
            fontSize: 24 * fontSizeMultiplier
          }
        ]}>
          My Notes
        </Text>
        <Text style={[
          styles.headerSubtitle,
          {
            color: darkMode ? '#CCCCCC' : '#666666',
            fontSize: 16 * fontSizeMultiplier
          }
        ]}>
          {notes.length} note{notes.length !== 1 ? 's' : ''}
        </Text>
      </View>

      {/* Create Note Button */}
      {notes.length > 0 && (
        <View style={styles.createButtonContainer}>
          <TouchableOpacity
            style={styles.createButton}
            onPress={() => setShowCreateModal(true)}
          >
            <LinearGradient
              colors={['#f093fb', '#f5576c']}
              style={styles.createButtonGradient}
            >
              <Ionicons name="add" size={20} color="#FFFFFF" />
              <Text style={[
                styles.createButtonText,
                { fontSize: 14 * fontSizeMultiplier }
              ]}>
                Create New Note
              </Text>
            </LinearGradient>
          </TouchableOpacity>
        </View>
      )}

      {notes.length > 0 && (
        <>
          {/* Search Bar */}
          <View style={styles.searchContainer}>
            <View style={[
              styles.searchBar,
              { backgroundColor: darkMode ? '#2A2A2A' : '#F0F0F0' }
            ]}>
              <Ionicons name="search" size={20} color={darkMode ? '#CCCCCC' : '#666666'} />
              <TextInput
                style={[
                  styles.searchInput,
                  {
                    color: darkMode ? '#FFFFFF' : '#333333',
                    fontSize: 14 * fontSizeMultiplier
                  }
                ]}
                placeholder="Search notes..."
                placeholderTextColor={darkMode ? '#AAAAAA' : '#999999'}
                value={searchQuery}
                onChangeText={setSearchQuery}
              />
              {searchQuery.length > 0 && (
                <TouchableOpacity onPress={() => setSearchQuery('')}>
                  <Ionicons name="close" size={20} color={darkMode ? '#CCCCCC' : '#666666'} />
                </TouchableOpacity>
              )}
            </View>
          </View>

          {/* Category Filter */}
          <View style={styles.categoryContainer}>
            <ScrollView
              horizontal
              showsHorizontalScrollIndicator={false}
              contentContainerStyle={styles.categoryScrollContent}
            >
              {categories.map((category) => (
                <TouchableOpacity
                  key={category}
                  style={[
                    styles.categoryButton,
                    selectedCategory === category && styles.selectedCategoryButton
                  ]}
                  onPress={() => setSelectedCategory(category)}
                >
                  <Text style={[
                    styles.categoryButtonText,
                    { fontSize: 12 * fontSizeMultiplier },
                    selectedCategory === category && styles.selectedCategoryButtonText
                  ]}>
                    {category}
                  </Text>
                </TouchableOpacity>
              ))}
            </ScrollView>
          </View>
        </>
      )}

      {/* Notes List */}
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {filteredNotes.length === 0 ? (
          notes.length === 0 ? renderEmptyState() : (
            <View style={styles.noResultsState}>
              <Ionicons name="search" size={48} color="#CCCCCC" />
              <Text style={[
                styles.noResultsText,
                {
                  color: darkMode ? '#CCCCCC' : '#666666',
                  fontSize: 16 * fontSizeMultiplier
                }
              ]}>
                No notes match your search
              </Text>
            </View>
          )
        ) : (
          filteredNotes.map((note, index) => renderNoteCard(note, index))
        )}
      </ScrollView>

      {/* Create/Edit Note Modal */}
      <Modal
        visible={showCreateModal}
        transparent={true}
        animationType="slide"
        onRequestClose={resetModal}
      >
        <View style={styles.modalOverlay}>
          <View style={[
            styles.modalContent,
            { backgroundColor: darkMode ? '#2A2A2A' : '#FFFFFF' }
          ]}>
            <Text style={[
              styles.modalTitle,
              {
                color: darkMode ? '#FFFFFF' : '#333333',
                fontSize: 20 * fontSizeMultiplier
              }
            ]}>
              {editingNote ? 'Edit Note' : 'Create New Note'}
            </Text>

            <TextInput
              style={[
                styles.modalInput,
                {
                  backgroundColor: darkMode ? '#3A3A3A' : '#F0F0F0',
                  color: darkMode ? '#FFFFFF' : '#333333',
                  fontSize: 16 * fontSizeMultiplier
                }
              ]}
              placeholder="Note title"
              placeholderTextColor={darkMode ? '#AAAAAA' : '#999999'}
              value={newNoteTitle}
              onChangeText={setNewNoteTitle}
              maxLength={100}
            />

            {/* Category Selector */}
            <View style={styles.categorySelector}>
              <Text style={[
                styles.categorySelectorLabel,
                {
                  color: darkMode ? '#CCCCCC' : '#666666',
                  fontSize: 14 * fontSizeMultiplier
                }
              ]}>
                Category:
              </Text>
              <ScrollView
                horizontal
                showsHorizontalScrollIndicator={false}
                style={styles.categorySelectorScroll}
              >
                {categories.slice(1).map((category) => (
                  <TouchableOpacity
                    key={category}
                    style={[
                      styles.categorySelectorButton,
                      newNoteCategory === category && styles.selectedCategorySelectorButton
                    ]}
                    onPress={() => setNewNoteCategory(category)}
                  >
                    <Text style={[
                      styles.categorySelectorButtonText,
                      { fontSize: 12 * fontSizeMultiplier },
                      newNoteCategory === category && styles.selectedCategorySelectorButtonText
                    ]}>
                      {category}
                    </Text>
                  </TouchableOpacity>
                ))}
              </ScrollView>
            </View>

            <TextInput
              style={[
                styles.modalInput,
                styles.modalTextArea,
                {
                  backgroundColor: darkMode ? '#3A3A3A' : '#F0F0F0',
                  color: darkMode ? '#FFFFFF' : '#333333',
                  fontSize: 14 * fontSizeMultiplier
                }
              ]}
              placeholder="Write your note here..."
              placeholderTextColor={darkMode ? '#AAAAAA' : '#999999'}
              value={newNoteContent}
              onChangeText={setNewNoteContent}
              multiline={true}
              numberOfLines={8}
              maxLength={5000}
            />

            <View style={styles.modalButtons}>
              <TouchableOpacity
                style={[styles.modalButton, styles.cancelButton]}
                onPress={resetModal}
              >
                <Text style={[
                  styles.cancelButtonText,
                  { fontSize: 14 * fontSizeMultiplier }
                ]}>
                  Cancel
                </Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.modalButton, styles.saveButton]}
                onPress={createOrUpdateNote}
              >
                <Text style={[
                  styles.saveButtonText,
                  { fontSize: 14 * fontSizeMultiplier }
                ]}>
                  {editingNote ? 'Update' : 'Save'}
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>

      {/* Enhanced Banner Ad */}
      <View style={styles.adContainer}>
        <BannerAdComponent
          placement={AD_PLACEMENTS.NOTES_BANNER}
          fallbackToWebView={true}
          enableRefresh={true}
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  darkContainer: {
    backgroundColor: '#121212',
  },
  lightContainer: {
    backgroundColor: '#F5F5F5',
  },
  centerContent: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  header: {
    padding: 20,
    paddingTop: 40,
    alignItems: 'center',
  },
  headerTitle: {
    fontWeight: 'bold',
    marginBottom: 8,
  },
  headerSubtitle: {
    textAlign: 'center',
  },
  loadingText: {
    fontSize: 18,
    textAlign: 'center',
  },
  createButtonContainer: {
    paddingHorizontal: 20,
    marginBottom: 20,
  },
  createButton: {
    borderRadius: 25,
    overflow: 'hidden',
  },
  createButtonGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 20,
  },
  createButtonText: {
    color: '#FFFFFF',
    fontWeight: '500',
    marginLeft: 8,
  },
  searchContainer: {
    paddingHorizontal: 20,
    marginBottom: 16,
  },
  searchBar: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 25,
  },
  searchInput: {
    flex: 1,
    marginLeft: 12,
    marginRight: 8,
  },
  categoryContainer: {
    marginBottom: 20,
  },
  categoryScrollContent: {
    paddingHorizontal: 20,
  },
  categoryButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    marginRight: 8,
    borderRadius: 20,
    backgroundColor: '#E0E0E0',
  },
  selectedCategoryButton: {
    backgroundColor: '#667eea',
  },
  categoryButtonText: {
    color: '#666666',
    fontWeight: '500',
  },
  selectedCategoryButtonText: {
    color: '#FFFFFF',
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  noteCard: {
    borderRadius: 12,
    marginBottom: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
  },
  noteContent: {
    padding: 16,
  },
  noteHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  noteTitleContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  noteTitle: {
    fontWeight: 'bold',
    flex: 1,
    marginRight: 8,
  },
  categoryBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  categoryText: {
    color: '#FFFFFF',
    fontWeight: 'bold',
  },
  noteActions: {
    flexDirection: 'row',
  },
  actionButton: {
    padding: 4,
    marginLeft: 8,
  },
  notePreview: {
    lineHeight: 20,
    marginBottom: 12,
  },
  noteFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderTopWidth: 1,
    borderTopColor: 'rgba(0, 0, 0, 0.1)',
    paddingTop: 8,
  },
  dateText: {
    fontStyle: 'italic',
  },
  noteStats: {
    flexDirection: 'row',
  },
  statsText: {
    fontStyle: 'italic',
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: 60,
    paddingHorizontal: 40,
  },
  emptyStateTitle: {
    fontWeight: 'bold',
    marginTop: 16,
    marginBottom: 8,
  },
  emptyStateText: {
    textAlign: 'center',
    lineHeight: 22,
    marginBottom: 24,
  },
  createFirstButton: {
    borderRadius: 25,
    overflow: 'hidden',
  },
  createFirstGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 24,
    paddingVertical: 12,
  },
  createFirstText: {
    color: '#FFFFFF',
    fontWeight: '500',
    marginLeft: 8,
  },
  noResultsState: {
    alignItems: 'center',
    paddingVertical: 60,
  },
  noResultsText: {
    marginTop: 16,
    textAlign: 'center',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    width: '95%',
    maxWidth: 500,
    maxHeight: '90%',
    borderRadius: 16,
    padding: 24,
  },
  modalTitle: {
    fontWeight: 'bold',
    marginBottom: 20,
    textAlign: 'center',
  },
  modalInput: {
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    marginBottom: 16,
  },
  categorySelector: {
    marginBottom: 16,
  },
  categorySelectorLabel: {
    marginBottom: 8,
    fontWeight: '500',
  },
  categorySelectorScroll: {
    flexGrow: 0,
  },
  categorySelectorButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    marginRight: 8,
    borderRadius: 15,
    backgroundColor: '#E0E0E0',
  },
  selectedCategorySelectorButton: {
    backgroundColor: '#667eea',
  },
  categorySelectorButtonText: {
    color: '#666666',
    fontWeight: '500',
  },
  selectedCategorySelectorButtonText: {
    color: '#FFFFFF',
  },
  modalTextArea: {
    height: 120,
    textAlignVertical: 'top',
  },
  modalButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 8,
  },
  modalButton: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  cancelButton: {
    backgroundColor: '#E0E0E0',
    marginRight: 8,
  },
  cancelButtonText: {
    color: '#666666',
    fontWeight: '500',
  },
  saveButton: {
    backgroundColor: '#667eea',
    marginLeft: 8,
  },
  saveButtonText: {
    color: '#FFFFFF',
    fontWeight: '500',
  },
  adContainer: {
    width: '100%',
    alignItems: 'center',
    backgroundColor: 'transparent',
  },
});

export default NotesScreen;
