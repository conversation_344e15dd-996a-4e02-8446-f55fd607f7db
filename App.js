import React, { useContext, useState, useEffect } from 'react';
import { createStackNavigator } from '@react-navigation/stack';
import { createDrawerNavigator, DrawerContentScrollView, DrawerItemList } from '@react-navigation/drawer';
import { NavigationContainer } from '@react-navigation/native';
import { View, Text, StyleSheet, ActivityIndicator, Animated, TouchableOpacity, Alert } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useFonts, Poppins_400Regular, Poppins_500Medium, Poppins_600SemiBold, Poppins_700Bold } from '@expo-google-fonts/poppins';
import * as SplashScreen from 'expo-splash-screen';
import { LinearGradient } from 'expo-linear-gradient';

// Safe imports with error handling
let auth, onAuthStateChanged, ThemeProvider, ThemeContext, initializeAdMob, AdManager, notificationService;

try {
  const firebaseAuth = require('./firebaseConfig');
  auth = firebaseAuth.auth;
  const firebaseAuthFunctions = require('firebase/auth');
  onAuthStateChanged = firebaseAuthFunctions.onAuthStateChanged;
} catch (error) {
  console.error('Firebase import error:', error);
  // Create mock auth for fallback
  auth = { currentUser: null };
  onAuthStateChanged = () => () => {};
}

try {
  const themeContext = require('./screens/ThemeContext');
  ThemeProvider = themeContext.ThemeProvider;
  ThemeContext = themeContext.ThemeContext;
} catch (error) {
  console.error('ThemeContext import error:', error);
  // Create fallback theme context
  ThemeContext = React.createContext({ darkMode: false, soundEnabled: true, fontSizeMultiplier: 1 });
  ThemeProvider = ({ children }) => (
    <ThemeContext.Provider value={{ darkMode: false, soundEnabled: true, fontSizeMultiplier: 1 }}>
      {children}
    </ThemeContext.Provider>
  );
}

try {
  const adMobService = require('./services/AdMobService');
  initializeAdMob = adMobService.initializeAdMob;
} catch (error) {
  console.error('AdMobService import error:', error);
  initializeAdMob = () => Promise.resolve({ fallback: true, error: 'AdMob not available' });
}

try {
  const adManagerModule = require('./src/services/AdManager');
  AdManager = adManagerModule.default;
} catch (error) {
  console.error('AdManager import error:', error);
  AdManager = { initialize: () => Promise.resolve() };
}

try {
  notificationService = require('./services/NotificationService').default;
} catch (error) {
  console.error('NotificationService import error:', error);
  notificationService = {
    initialize: () => Promise.resolve(false),
    cleanup: () => {}
  };
}

import HomeScreen from './screens/HomeScreen';
import QuizScreen from './screens/QuizScreen';
import QuizLevels from './screens/QuizLevels';
import QuizQuestions from './screens/QuizQuestions';
import SettingsScreen from './screens/SettingsScreen'; // Import the actual SettingsScreen
import LoginScreen from './screens/LoginScreen';
import RegisterScreen from './screens/RegisterScreen';
import ForgotPasswordScreen from './screens/ForgotPasswordScreen';
import ChangePasswordScreen from './screens/ChangePasswordScreen';
import AiChatScreen from './screens/AiChatScreen'; // Import AiChatScreen
// Import Study Tools screens
import StudyToolsScreen from './screens/StudyToolsScreen';
import FlashcardsScreen from './screens/FlashcardsScreen';
import FormulaSheetsScreen from './screens/FormulaSheetsScreen';
import PracticeTestsScreen from './screens/PracticeTestsScreen';
import MockExamsScreen from './screens/MockExamsScreen';
import AdaptiveLearningScreen from './screens/AdaptiveLearningScreen';
import SpacedRepetitionScreen from './screens/SpacedRepetitionScreen';
// Import Content Organization screens
import FavoritesScreen from './screens/FavoritesScreen';
import StudyPlaylistsScreen from './screens/StudyPlaylistsScreen';
import NotesScreen from './screens/NotesScreen';
import TimedQuiz from './screens/TimedQuiz';
// Import screens directly instead of lazy loading to avoid component resolution issues
import ChapterScreen from './screens/ChapterScreen';
import VideoChapterScreen from './screens/VideoChapterScreen';
import BookUnitsScreen from './screens/BookUnitsScreen';
import PdfViewerScreen from './screens/PdfViewerScreen';
// Keep essential/frequent screens imported directly for potentially faster access after initial load
// import ChapterScreen from './screens/ChapterScreen';
// import VideoChapterScreen from './screens/VideoChapterScreen';
// import BookUnitsScreen from './screens/BookUnitsScreen';


const Stack = createStackNavigator();
const Drawer = createDrawerNavigator();

// Error Boundary Component
class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error) {
    return { hasError: true, error };
  }

  componentDidCatch(error, errorInfo) {
    console.error('App Error Boundary caught an error:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', padding: 20 }}>
          <Text style={{ fontSize: 18, fontWeight: 'bold', marginBottom: 10, textAlign: 'center' }}>
            Something went wrong
          </Text>
          <Text style={{ fontSize: 14, textAlign: 'center', marginBottom: 20 }}>
            Please restart the app. If the problem persists, contact support.
          </Text>
          <TouchableOpacity
            style={{ backgroundColor: '#007AFF', padding: 10, borderRadius: 5 }}
            onPress={() => this.setState({ hasError: false, error: null })}
          >
            <Text style={{ color: 'white', fontWeight: 'bold' }}>Try Again</Text>
          </TouchableOpacity>
        </View>
      );
    }

    return this.props.children;
  }
}

const CustomDrawerContent = (props) => {
  // Use the imported ThemeContext
  const { darkMode } = useContext(ThemeContext);
  // Removed unused navigation variable
  // Removed unused navigation variable
  // Removed unused handleLogout function

  return (
    // Use darkMode from the context
    <DrawerContentScrollView {...props} style={[styles.drawerContainer, { backgroundColor: darkMode ? '#121212' : '#fff' }]}>
      <View style={styles.drawerHeader}>
        {/* Use darkMode from the context */}
        <Text style={[styles.userName, { color: darkMode ? '#fff' : '#000' }]}>BeeTech</Text>
        <Text style={[styles.userEmail, { color: darkMode ? '#aaa' : '#555' }]}>Welcome to the App</Text>
      </View>
      <DrawerItemList {...props} />
    </DrawerContentScrollView>
  );
};

// Removed SettingsScreenUpdated component definition

const QuizStackNavigator = () => (
  <Stack.Navigator>
    <Stack.Screen name="QuizScreen" component={QuizScreen} options={{ title: 'Select Category' }} />
    <Stack.Screen name="QuizLevels" component={QuizLevels} options={{ title: 'Select Level' }} />
    <Stack.Screen name="QuizQuestions" component={QuizQuestions} options={{ title: 'Quiz Questions' }} />
  </Stack.Navigator>
);

const MainDrawerNavigator = () => {
  const { darkMode } = useContext(ThemeContext);

  return (
    <Drawer.Navigator
      drawerContent={(props) => <CustomDrawerContent {...props} />}
      screenOptions={{
        drawerStyle: { backgroundColor: darkMode ? '#121212' : '#fff' },
        drawerLabelStyle: { color: darkMode ? '#fff' : '#000' },
        drawerActiveTintColor: darkMode ? '#81b0ff' : '#000',
        drawerInactiveTintColor: darkMode ? '#aaa' : '#000',
      }}
    >
      <Drawer.Screen name="Home" component={HomeScreen} options={{ drawerIcon: ({ size, color }) => <Ionicons name="home" size={size} color={color} /> }} />
      <Drawer.Screen name="Quiz" component={QuizStackNavigator} options={{ drawerIcon: ({ size, color }) => <Ionicons name="game-controller-outline" size={size} color={color} /> }} />
      <Drawer.Screen name="Settings" component={SettingsScreen} options={{ drawerIcon: ({ size, color }) => <Ionicons name="settings" size={size} color={color} /> }} />
    </Drawer.Navigator>
  );
};

// Custom Loading Screen Component
const LoadingScreen = ({ fontsLoaded = false }) => {
  const { darkMode } = useContext(ThemeContext);
  const fadeAnim = React.useRef(new Animated.Value(0)).current;
  const scaleAnim = React.useRef(new Animated.Value(0.8)).current;
  const rotateAnim = React.useRef(new Animated.Value(0)).current;

  React.useEffect(() => {
    // Start animations
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 1000,
        useNativeDriver: true,
      }),
      Animated.spring(scaleAnim, {
        toValue: 1,
        friction: 4,
        useNativeDriver: true,
      }),
    ]).start();

    // Continuous rotation for loading indicator
    Animated.loop(
      Animated.timing(rotateAnim, {
        toValue: 1,
        duration: 2000,
        useNativeDriver: true,
      })
    ).start();
  }, []);

  const spin = rotateAnim.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '360deg'],
  });

  return (
    <LinearGradient
      colors={darkMode ? ['#1a1a2e', '#16213e', '#0f3460'] : ['#667eea', '#764ba2', '#f093fb']}
      style={styles.loadingContainer}
    >
      <Animated.View style={[styles.loadingContent, { opacity: fadeAnim }]}>
        <Animated.Image
          source={require('./assets/images/logo.png')}
          style={[
            styles.loadingLogo,
            {
              transform: [{ scale: scaleAnim }],
            },
          ]}
        />
        <Animated.Text style={[
          styles.appName,
          {
            color: '#fff',
            fontFamily: fontsLoaded ? 'Poppins_700Bold' : 'System'
          }
        ]}>
          BeeTech
        </Animated.Text>
        <Animated.Text style={[
          styles.appTagline,
          {
            color: darkMode ? '#ccc' : '#f0f0f0',
            fontFamily: fontsLoaded ? 'Poppins_400Regular' : 'System'
          }
        ]}>
          Enhance your knowledge with fun quizzes!
        </Animated.Text>

        <Animated.View style={[styles.loadingIndicatorContainer, { transform: [{ rotate: spin }] }]}>
          <ActivityIndicator size="large" color={darkMode ? '#81b0ff' : '#ffffff'} />
        </Animated.View>

        <Animated.Text style={[
          styles.loadingText,
          {
            color: darkMode ? '#aaa' : '#e0e0e0',
            fontFamily: fontsLoaded ? 'Poppins_400Regular' : 'System'
          }
        ]}>
          Loading...
        </Animated.Text>
      </Animated.View>
    </LinearGradient>
  );
};

function RootNavigator() {
  const [authInitializing, setAuthInitializing] = useState(true);
  const [user, setUser] = useState(null);
  const [appReady, setAppReady] = useState(false);

  // Safe font loading with error handling
  let [fontsLoaded, fontError] = useFonts({
    Poppins_400Regular,
    Poppins_500Medium,
    Poppins_600SemiBold,
    Poppins_700Bold,
  });

  // App initialization effect with timeout
  useEffect(() => {
    const initializeApp = async () => {
      try {
        console.log("Starting app initialization...");

        // Wait for fonts to load or fail
        if (fontsLoaded || fontError) {
          console.log("Fonts status:", { fontsLoaded, fontError: !!fontError });
          setAppReady(true);
        }
      } catch (error) {
        console.error("App initialization error:", error);
        setAppReady(true); // Continue even if there's an error
      }
    };

    // Add timeout to prevent infinite loading
    const timeoutId = setTimeout(() => {
      console.warn("App initialization timeout - forcing app to start");
      setAppReady(true);
    }, 15000); // 15 second timeout

    initializeApp();

    return () => clearTimeout(timeoutId);
  }, [fontsLoaded, fontError]);

  // Initialize AdMob and AdManager with enhanced error handling
  useEffect(() => {
    const initializeAds = async () => {
      try {
        console.log("Initializing AdMob and AdManager...");

        // Initialize AdMob first with timeout
        const adMobResult = await Promise.race([
          initializeAdMob(),
          new Promise((_, reject) =>
            setTimeout(() => reject(new Error('AdMob initialization timeout')), 10000)
          )
        ]);

        if (adMobResult.fallback) {
          console.log("AdMob using fallback mode:", adMobResult.error || 'Native ads not available');
        } else {
          console.log("AdMob initialized successfully");
        }

        // Initialize AdManager with timeout
        await Promise.race([
          AdManager.initialize(),
          new Promise((_, reject) =>
            setTimeout(() => reject(new Error('AdManager initialization timeout')), 5000)
          )
        ]);

        console.log("AdManager initialized successfully");
      } catch (error) {
        console.error("Ad initialization failed:", error);
        // App continues to work even if ads fail completely
      }
    };

    // Only initialize ads if app is ready
    if (appReady) {
      initializeAds();
    }
  }, [appReady]);

  // Initialize notification service with enhanced error handling
  useEffect(() => {
    const initializeNotifications = async () => {
      try {
        // Add timeout for notification service
        const success = await Promise.race([
          notificationService.initialize(),
          new Promise((resolve) =>
            setTimeout(() => resolve(false), 5000) // 5 second timeout
          )
        ]);

        if (success) {
          console.log('Notification service initialized successfully');
        } else {
          console.log('Notification service not available or timed out');
        }
      } catch (error) {
        console.error('Failed to initialize notification service:', error);
        // Continue without notifications
      }
    };

    // Only initialize notifications if app is ready
    if (appReady) {
      initializeNotifications();
    }

    // Cleanup on unmount
    return () => {
      try {
        notificationService.cleanup();
      } catch (error) {
        console.error('Error cleaning up notification service:', error);
      }
    };
  }, [appReady]);

  // Handle user state changes (Firebase Auth) with error handling
  useEffect(() => {
    let subscriber = null;

    const setupAuth = async () => {
      try {
        console.log("Setting up Firebase auth state listener...");

        subscriber = onAuthStateChanged(auth, (user) => {
          try {
            console.log("onAuthStateChanged fired. User:", user ? user.uid : null);
            setUser(user);
            if (authInitializing) {
              console.log("Setting authInitializing to false.");
              setAuthInitializing(false);
            }
          } catch (error) {
            console.error("Error in auth state change handler:", error);
            setAuthInitializing(false); // Ensure we don't get stuck
          }
        });
      } catch (error) {
        console.error("Error setting up Firebase auth:", error);
        setAuthInitializing(false); // Continue without auth
      }
    };

    // Only setup auth if app is ready
    if (appReady) {
      setupAuth();
    }

    return () => {
      try {
        if (subscriber) {
          console.log("Unsubscribing Firebase auth state listener.");
          subscriber();
        }
      } catch (error) {
        console.error("Error unsubscribing from auth:", error);
      }
    };
  }, [appReady, authInitializing]);

  // Prevent splash screen from hiding until fonts are loaded and auth is checked
  useEffect(() => {
    async function prepare() {
      await SplashScreen.preventAutoHideAsync();
    }
    prepare();
  }, []);

  // Hide splash screen once fonts are loaded/error and auth is initialized
  useEffect(() => {
    if ((fontsLoaded || fontError) && !authInitializing) {
      async function hideSplash() {
        await SplashScreen.hideAsync();
      }
      hideSplash();
    }
  }, [fontsLoaded, fontError, authInitializing]);

  // Show loading screen while app is initializing
  if (!appReady || authInitializing) {
    return <LoadingScreen fontsLoaded={fontsLoaded} />; // Show custom loading screen
  }

  // Log font loading error if it occurs but continue with app
  if (fontError) {
    console.warn("Font Loading Error (continuing with system fonts):", fontError);
    // Continue with system fonts - don't block the app
  }

 return (
   <NavigationContainer>
     <Stack.Navigator>
         {user ? (
           // User is signed in, show main app with drawer
          <>
            <Stack.Screen
              name="MainApp"
              component={MainDrawerNavigator}
              options={{ headerShown: false }}
            />
            {/* Keep other screens accessible from MainApp */}
            <Stack.Screen
              name="ChangePassword"
              component={ChangePasswordScreen}
              options={{ headerShown: false }} // Screen has its own header/back button
            />
            <Stack.Screen
              name="AiChat"
              component={AiChatScreen}
              options={{ title: 'Ask AI Doubt' }} // Set a title for the header
            />
            {/* Study Tools Screens */}
            <Stack.Screen
              name="StudyTools"
              component={StudyToolsScreen}
              options={{ title: 'Study Tools' }}
            />
            <Stack.Screen
              name="Flashcards"
              component={FlashcardsScreen}
              options={{ title: 'Flashcards' }}
            />
            <Stack.Screen
              name="FormulaSheets"
              component={FormulaSheetsScreen}
              options={{ title: 'Formula Sheets' }}
            />
            <Stack.Screen
              name="PracticeTests"
              component={PracticeTestsScreen}
              options={{ title: 'Practice Tests' }}
            />
            <Stack.Screen
              name="MockExams"
              component={MockExamsScreen}
              options={{ title: 'Mock Exams' }}
            />
            <Stack.Screen
              name="AdaptiveLearning"
              component={AdaptiveLearningScreen}
              options={{ title: 'AI Study Plan' }}
            />
            <Stack.Screen
              name="SpacedRepetition"
              component={SpacedRepetitionScreen}
              options={{ title: 'Spaced Repetition' }}
            />
            {/* Content Organization Screens */}
            <Stack.Screen
              name="Favorites"
              component={FavoritesScreen}
              options={{ title: 'Favorites' }}
            />
            <Stack.Screen
              name="StudyPlaylists"
              component={StudyPlaylistsScreen}
              options={{ title: 'Study Playlists' }}
            />
            <Stack.Screen
              name="Notes"
              component={NotesScreen}
              options={{ title: 'My Notes' }}
            />
            {/* Add TimedQuiz screen for mock exams and practice tests */}
            <Stack.Screen
              name="TimedQuiz"
              component={TimedQuiz}
              options={{ headerShown: false }} // TimedQuiz has its own header
            />
            {/* Add ChapterScreen to the stack */}
            <Stack.Screen
              name="ChapterScreen"
              component={ChapterScreen}
              options={({ route }) => ({ title: `${route.params.category} Chapters` })}
            />
            {/* Add VideoChapterScreen to the stack */}
            <Stack.Screen
              name="VideoChapterScreen"
              component={VideoChapterScreen}
              options={({ route }) => ({ title: `${route.params.category} Video Chapters` })} // Dynamic title
            />
            {/* Add BookUnitsScreen to the stack */}
            <Stack.Screen
              name="BookUnitsScreen"
              component={BookUnitsScreen}
              options={({ route }) => ({ title: route.params.bookTitle || 'Book Units' })} // Dynamic title based on book
            />
            {/* Add PdfViewerScreen to the stack */}
            <Stack.Screen
              name="PdfViewer"
              component={PdfViewerScreen}
              options={{ title: 'PDF Viewer' }} // You can customize the title
            />
          </>
         ) : (
          // No user is signed in, show auth screens
          <>
            <Stack.Screen
              name="Login"
              component={LoginScreen}
              options={{ headerShown: false }}
            />
            <Stack.Screen
              name="Register"
              component={RegisterScreen}
              options={{ headerShown: false }}
            />
            <Stack.Screen
              name="ForgotPassword"
              component={ForgotPasswordScreen}
              options={{ headerShown: false }}
            />
          </>
        )}
         {/* Screens accessible regardless of auth state (if any needed) */}
        {/* Example: A public info screen */}
        {/* <Stack.Screen name="PublicInfo" component={PublicInfoScreen} /> */}
       </Stack.Navigator>
   </NavigationContainer>
 );
}


// Safe App wrapper with multiple layers of error protection
function SafeApp() {
  try {
    return (
      <ErrorBoundary>
        <ThemeProvider>
          <RootNavigator />
        </ThemeProvider>
      </ErrorBoundary>
    );
  } catch (error) {
    console.error("Critical app error:", error);
    // Fallback UI if everything fails
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', padding: 20 }}>
        <Text style={{ fontSize: 18, fontWeight: 'bold', marginBottom: 10, textAlign: 'center' }}>
          App Error
        </Text>
        <Text style={{ fontSize: 14, textAlign: 'center' }}>
          Please restart the app
        </Text>
      </View>
    );
  }
}

export default function App() {
  return <SafeApp />;
}


const styles = StyleSheet.create({
  drawerContainer: { flex: 1 },
  drawerHeader: { padding: 20, alignItems: 'center', borderBottomWidth: 1, borderBottomColor: '#ccc' },
  userName: { fontSize: 18, fontWeight: 'bold', marginBottom: 5 },
  userEmail: { fontSize: 14 },
  // Loading Screen Styles
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingContent: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  loadingLogo: {
    width: 120,
    height: 120,
    marginBottom: 20,
    borderRadius: 60,
  },
  appName: {
    fontSize: 32,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  appTagline: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 40,
    paddingHorizontal: 20,
  },
  loadingIndicatorContainer: {
    marginBottom: 20,
  },
  loadingText: {
    fontSize: 14,
  },
  // Removed themeToggleContainer and themeToggleText styles
  // Removed container and settingRow styles (they belonged to SettingsScreenUpdated)
});
