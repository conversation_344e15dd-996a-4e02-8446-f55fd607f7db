<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet config=":expo-modules-core" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\quiz-bee-techs\node_modules\expo-modules-core\android\build\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":expo-updates-interface" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\quiz-bee-techs\node_modules\expo-updates-interface\android\build\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":expo-dev-menu-interface" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\quiz-bee-techs\node_modules\expo-dev-menu-interface\android\build\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":expo-system-ui" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\quiz-bee-techs\node_modules\expo-system-ui\android\build\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":expo-splash-screen" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\quiz-bee-techs\node_modules\expo-splash-screen\android\build\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":expo-speech" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\quiz-bee-techs\node_modules\expo-speech\android\build\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":expo-sharing" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\quiz-bee-techs\node_modules\expo-sharing\android\build\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":expo-notifications" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":expo-linear-gradient" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\quiz-bee-techs\node_modules\expo-linear-gradient\android\build\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":expo-keep-awake" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\quiz-bee-techs\node_modules\expo-keep-awake\android\build\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":expo-json-utils" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\quiz-bee-techs\node_modules\expo-json-utils\android\build\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":expo-manifests" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\quiz-bee-techs\node_modules\expo-manifests\android\build\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":expo-image-picker" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\quiz-bee-techs\node_modules\expo-image-picker\android\build\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":expo-image-loader" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\quiz-bee-techs\node_modules\expo-image-loader\android\build\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":expo-haptics" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\quiz-bee-techs\node_modules\expo-haptics\android\build\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":expo-font" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\quiz-bee-techs\node_modules\expo-font\android\build\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":expo-file-system" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\quiz-bee-techs\node_modules\expo-file-system\android\build\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":expo-device" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\quiz-bee-techs\node_modules\expo-device\android\build\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":expo-dev-menu" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\quiz-bee-techs\node_modules\expo-dev-menu\android\build\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":expo-dev-launcher" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\quiz-bee-techs\node_modules\expo-dev-launcher\android\build\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":expo-dev-client" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\quiz-bee-techs\node_modules\expo-dev-client\android\build\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":expo-constants" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\quiz-bee-techs\node_modules\expo-constants\android\build\intermediates\library_assets\release\packageReleaseAssets\out"><file name="app.config" path="C:\Users\<USER>\quiz-bee-techs\node_modules\expo-constants\android\build\intermediates\library_assets\release\packageReleaseAssets\out\app.config"/></source></dataSet><dataSet config=":expo-clipboard" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\quiz-bee-techs\node_modules\expo-clipboard\android\build\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":expo-av" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\quiz-bee-techs\node_modules\expo-av\android\build\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":expo-asset" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\quiz-bee-techs\node_modules\expo-asset\android\build\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":expo-application" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\quiz-bee-techs\node_modules\expo-application\android\build\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":react-native-vector-icons" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-vector-icons\android\build\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":react-native-reanimated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-reanimated\android\build\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":react-native-pdf" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-pdf\android\build\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":react-native-haptic-feedback" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-haptic-feedback\android\build\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":react-native-fast-image" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-fast-image\android\build\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":react-native-voice_voice" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\quiz-bee-techs\node_modules\@react-native-voice\voice\android\build\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":react-native-picker_picker" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\quiz-bee-techs\node_modules\@react-native-picker\picker\android\build\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":react-native-community_slider" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\quiz-bee-techs\node_modules\@react-native-community\slider\android\build\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":react-native-clipboard_clipboard" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\quiz-bee-techs\node_modules\@react-native-clipboard\clipboard\android\build\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":react-native-async-storage_async-storage" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\quiz-bee-techs\node_modules\@react-native-async-storage\async-storage\android\build\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":react-native-webview" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-webview\android\build\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":react-native-screens" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-screens\android\build\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":react-native-safe-area-context" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-safe-area-context\android\build\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":react-native-google-mobile-ads" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-google-mobile-ads\android\build\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":react-native-gesture-handler" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-gesture-handler\android\build\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":expo" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\quiz-bee-techs\node_modules\expo\android\build\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\quiz-bee-techs\android\app\src\main\assets"/></dataSet><dataSet config="release" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\quiz-bee-techs\android\app\src\release\assets"/></dataSet><dataSet config="assets-createBundleReleaseJsAndAssets" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\quiz-bee-techs\android\app\build\generated\assets\createBundleReleaseJsAndAssets"><file name="index.android.bundle" path="C:\Users\<USER>\quiz-bee-techs\android\app\build\generated\assets\createBundleReleaseJsAndAssets\index.android.bundle"/></source></dataSet><dataSet config="generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\quiz-bee-techs\android\app\build\intermediates\shader_assets\release\compileReleaseShaders\out"/></dataSet></merger>