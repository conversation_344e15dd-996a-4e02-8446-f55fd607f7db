import { PDF_URLS, getPdfUrl, isPdfAvailable } from '../config/pdfConfig';
import * as FileSystem from 'expo-file-system';
import { Alert } from 'react-native';

class PdfService {
  constructor() {
    this.cache = new Map();
    this.downloadQueue = new Set();
  }

  /**
   * Get PDF URL for a specific subject and type
   */
  getPdfUrl(subject, type, chapter = null) {
    return getPdfUrl(subject, type, chapter);
  }

  /**
   * Check if PDF is available
   */
  isPdfAvailable(subject, type, chapter = null) {
    return isPdfAvailable(subject, type, chapter);
  }

  /**
   * Open PDF in the in-app PDF viewer
   */
  async openPdf(subject, type, chapter = null, navigation = null) {
    try {
      const url = this.getPdfUrl(subject, type, chapter);

      if (!url) {
        Alert.alert('Error', 'PDF not available for this subject/chapter');
        return false;
      }

      // For remote URLs, validate accessibility (but don't fail immediately)
      if (url.startsWith('http')) {
        try {
          const response = await Promise.race([
            fetch(url, { method: 'HEAD' }),
            new Promise((_, reject) =>
              setTimeout(() => reject(new Error('Timeout')), 5000)
            )
          ]);

          if (!response.ok) {
            console.warn(`PDF URL returned ${response.status}, but proceeding anyway`);
          }
        } catch (fetchError) {
          console.warn('PDF URL validation failed, but proceeding anyway:', fetchError);
        }
      }

      // Check if we have a cached version first
      const cachedPath = await this.getCachedPdfPath(subject, type, chapter);

      if (cachedPath && await FileSystem.getInfoAsync(cachedPath).then(info => info.exists)) {
        console.log('Using cached PDF:', cachedPath);
        // Open cached version
        await this.openPdfFile(cachedPath, navigation);
        return true;
      }

      // Open remote URL directly
      console.log('Opening remote PDF:', url);
      await this.openPdfFile(url, navigation);
      return true;

    } catch (error) {
      console.error('Error opening PDF:', error);
      Alert.alert('Error', `Failed to open PDF: ${error.message}`);
      return false;
    }
  }

  /**
   * Download PDF for offline viewing
   */
  async downloadPdf(subject, type, chapter = null, onProgress = null) {
    try {
      const url = this.getPdfUrl(subject, type, chapter);
      const filename = this.getPdfFilename(subject, type, chapter);
      
      if (!url) {
        throw new Error('PDF URL not available');
      }

      if (this.downloadQueue.has(filename)) {
        console.log('PDF already downloading:', filename);
        return false;
      }

      this.downloadQueue.add(filename);

      const localPath = `${FileSystem.documentDirectory}pdfs/${filename}`;
      
      // Ensure directory exists
      await FileSystem.makeDirectoryAsync(`${FileSystem.documentDirectory}pdfs/`, { 
        intermediates: true 
      });

      // Download with progress tracking
      const downloadResumable = FileSystem.createDownloadResumable(
        url,
        localPath,
        {},
        (downloadProgress) => {
          const progress = downloadProgress.totalBytesWritten / downloadProgress.totalBytesExpectedToWrite;
          if (onProgress) {
            onProgress(progress);
          }
        }
      );

      const result = await downloadResumable.downloadAsync();
      
      if (result && result.uri) {
        this.cache.set(filename, result.uri);
        console.log('PDF downloaded successfully:', filename);
        return result.uri;
      }

      throw new Error('Download failed');

    } catch (error) {
      console.error('Error downloading PDF:', error);
      throw error;
    } finally {
      const filename = this.getPdfFilename(subject, type, chapter);
      this.downloadQueue.delete(filename);
    }
  }

  /**
   * Get cached PDF path
   */
  async getCachedPdfPath(subject, type, chapter = null) {
    const filename = this.getPdfFilename(subject, type, chapter);
    
    if (this.cache.has(filename)) {
      return this.cache.get(filename);
    }

    const localPath = `${FileSystem.documentDirectory}pdfs/${filename}`;
    const fileInfo = await FileSystem.getInfoAsync(localPath);
    
    if (fileInfo.exists) {
      this.cache.set(filename, localPath);
      return localPath;
    }

    return null;
  }

  /**
   * Check if PDF is cached locally
   */
  async isPdfCached(subject, type, chapter = null) {
    const cachedPath = await this.getCachedPdfPath(subject, type, chapter);
    return cachedPath !== null;
  }

  /**
   * Get PDF filename
   */
  getPdfFilename(subject, type, chapter = null) {
    if (chapter) {
      return `${subject}_${type}_chapter${chapter}.pdf`;
    }
    return `${subject}_${type}.pdf`;
  }

  /**
   * Open PDF file using the in-app PDF viewer
   */
  async openPdfFile(uri, navigation) {
    try {
      console.log('Opening PDF with URI:', uri);

      // Always navigate to the in-app PDF viewer
      if (navigation) {
        navigation.navigate('PdfViewer', { sourceUri: uri });
        return true;
      }

      // Fallback: if no navigation, try sharing for local files or linking for remote
      const isLocalFile = uri && (uri.startsWith('file://') || uri.includes('DocumentDirectory') || uri.includes('cacheDirectory'));

      if (isLocalFile) {
        // For local files, use sharing
        const Sharing = require('expo-sharing');
        if (await Sharing.isAvailableAsync()) {
          await Sharing.shareAsync(uri, {
            mimeType: 'application/pdf',
            dialogTitle: 'Open PDF with...'
          });
          return true;
        } else {
          throw new Error('File sharing not available');
        }
      } else {
        // For remote files, use linking
        const { Linking } = require('react-native');
        const canOpen = await Linking.canOpenURL(uri);
        if (canOpen) {
          await Linking.openURL(uri);
          return true;
        } else {
          throw new Error('Cannot open PDF - no suitable app found');
        }
      }
    } catch (error) {
      console.error('Error opening PDF file:', error);
      throw error;
    }
  }

  /**
   * Get all available PDFs for a subject
   */
  getAvailablePdfs(subject) {
    const subjectPdfs = PDF_URLS[subject];
    if (!subjectPdfs) return [];

    const pdfs = [];
    
    Object.keys(subjectPdfs).forEach(type => {
      if (typeof subjectPdfs[type] === 'string') {
        pdfs.push({ subject, type, title: `${subject} ${type}` });
      } else if (typeof subjectPdfs[type] === 'object') {
        Object.keys(subjectPdfs[type]).forEach(chapter => {
          pdfs.push({ 
            subject, 
            type, 
            chapter: chapter.replace('chapter', ''),
            title: `${subject} ${type} Chapter ${chapter.replace('chapter', '')}`
          });
        });
      }
    });

    return pdfs;
  }

  /**
   * Clear PDF cache
   */
  async clearCache() {
    try {
      const cacheDir = `${FileSystem.documentDirectory}pdfs/`;
      const dirInfo = await FileSystem.getInfoAsync(cacheDir);
      
      if (dirInfo.exists) {
        await FileSystem.deleteAsync(cacheDir);
      }
      
      this.cache.clear();
      console.log('PDF cache cleared');
    } catch (error) {
      console.error('Error clearing PDF cache:', error);
    }
  }

  /**
   * Get cache size
   */
  async getCacheSize() {
    try {
      const cacheDir = `${FileSystem.documentDirectory}pdfs/`;
      const dirInfo = await FileSystem.getInfoAsync(cacheDir);
      
      if (!dirInfo.exists) return 0;

      const files = await FileSystem.readDirectoryAsync(cacheDir);
      let totalSize = 0;

      for (const file of files) {
        const fileInfo = await FileSystem.getInfoAsync(`${cacheDir}${file}`);
        totalSize += fileInfo.size || 0;
      }

      return totalSize;
    } catch (error) {
      console.error('Error getting cache size:', error);
      return 0;
    }
  }
}

export default new PdfService();
