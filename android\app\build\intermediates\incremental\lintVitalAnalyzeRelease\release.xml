<variant
    name="release"
    package="com.gokul719.snack97152fc1f368437dac54171df4ba22bd"
    minSdkVersion="24"
    targetSdkVersion="34"
    mergedManifest="build\intermediates\merged_manifest\release\processReleaseMainManifest\AndroidManifest.xml"
    proguardFiles="build\intermediates\default_proguard_files\global\proguard-android.txt-8.6.0"
    partialResultsDir="build\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
    desugaredMethodsFiles="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\253b11e7a52b5a94be4045b38355fcde\transformed\D8BackportedDesugaredMethods.txt">
  <buildFeatures
      namespacing="REQUIRED"/>
  <sourceProviders>
    <sourceProvider
        manifests="src\main\AndroidManifest.xml"
        javaDirectories="src\main\java;build\generated\source\codegen\java;src\release\java;build\generated\autolinking\src\main\java;src\main\kotlin;src\release\kotlin"
        resDirectories="src\main\res;src\release\res"
        assetsDirectories="src\main\assets;src\release\assets"/>
  </sourceProviders>
  <testSourceProviders>
  </testSourceProviders>
  <testFixturesSourceProviders>
  </testFixturesSourceProviders>
  <resValues>
    <resValue
        type="integer"
        name="react_native_dev_server_port"
        value="8081" />
  </resValues>
  <artifact
      classOutputs="build\intermediates\javac\release\compileReleaseJavaWithJavac\classes;build\tmp\kotlin-classes\release;build\kotlinToolingMetadata;build\intermediates\compile_and_runtime_not_namespaced_r_class_jar\release\processReleaseResources\R.jar"
      type="MAIN"
      applicationId="com.gokul719.snack97152fc1f368437dac54171df4ba22bd"
      generatedSourceFolders="build\generated\ap_generated_sources\release\out;build\generated\source\buildConfig\release"
      generatedResourceFolders="build\generated\res\createBundleReleaseJsAndAssets;build\generated\res\resValues\release"
      desugaredMethodsFiles="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\253b11e7a52b5a94be4045b38355fcde\transformed\D8BackportedDesugaredMethods.txt">
  </artifact>
</variant>
