import React, { useState, useEffect, useRef } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Animated, Dimensions, Alert } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';

import { useRoute, useNavigation } from '@react-navigation/native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import BannerAdComponent from '../components/BannerAdComponent';
import { AD_PLACEMENTS } from '../src/config/adConfig';

const { width: screenWidth } = Dimensions.get('window');

const FlashcardsScreen = () => {
  const darkMode = false;
  const fontSizeMultiplier = 1;
  const route = useRoute();
  const navigation = useNavigation();
  const { category = 'NEET' } = route.params || {};

  const [currentIndex, setCurrentIndex] = useState(0);
  const [isFlipped, setIsFlipped] = useState(false);
  const [flashcards, setFlashcards] = useState([]);
  const [studiedCards, setStudiedCards] = useState(new Set());
  const [difficulty, setDifficulty] = useState('easy'); // easy, medium, hard

  const flipAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    loadFlashcards();
    loadProgress();
  }, [category, difficulty]);

  const loadFlashcards = () => {
    // Sample flashcard data - you can expand this or load from your questions.json
    const sampleFlashcards = [
      {
        id: 1,
        question: "What is the powerhouse of the cell?",
        answer: "Mitochondria - generates ATP, the energy currency of the cell",
        category: "Biology",
        difficulty: "easy"
      },
      {
        id: 2,
        question: "What is Newton's First Law of Motion?",
        answer: "An object at rest stays at rest and an object in motion stays in motion unless acted upon by an external force",
        category: "Physics",
        difficulty: "medium"
      },
      {
        id: 3,
        question: "What is the chemical formula for water?",
        answer: "H₂O - Two hydrogen atoms bonded to one oxygen atom",
        category: "Chemistry",
        difficulty: "easy"
      },
      {
        id: 4,
        question: "What is photosynthesis?",
        answer: "The process by which green plants use sunlight, carbon dioxide, and water to produce glucose and oxygen",
        category: "Biology",
        difficulty: "medium"
      },
      {
        id: 5,
        question: "What is the speed of light in vacuum?",
        answer: "299,792,458 meters per second (approximately 3 × 10⁸ m/s)",
        category: "Physics",
        difficulty: "hard"
      }
    ];

    // Filter by difficulty if needed
    const filteredCards = sampleFlashcards.filter(card =>
      card.difficulty === difficulty || difficulty === 'all'
    );

    setFlashcards(filteredCards);
    setCurrentIndex(0);
    setIsFlipped(false);
  };

  const loadProgress = async () => {
    try {
      const progress = await AsyncStorage.getItem(`flashcard_progress_${category}_${difficulty}`);
      if (progress) {
        setStudiedCards(new Set(JSON.parse(progress)));
      }
    } catch (error) {
      console.error('Error loading flashcard progress:', error);
    }
  };

  const saveProgress = async (newStudiedCards) => {
    try {
      await AsyncStorage.setItem(
        `flashcard_progress_${category}_${difficulty}`,
        JSON.stringify(Array.from(newStudiedCards))
      );
    } catch (error) {
      console.error('Error saving flashcard progress:', error);
    }
  };

  const flipCard = () => {
    Animated.timing(flipAnim, {
      toValue: isFlipped ? 0 : 1,
      duration: 600,
      useNativeDriver: true,
    }).start();
    setIsFlipped(!isFlipped);
  };

  const nextCard = () => {
    if (currentIndex < flashcards.length - 1) {
      // Mark current card as studied
      const newStudiedCards = new Set(studiedCards);
      newStudiedCards.add(flashcards[currentIndex].id);
      setStudiedCards(newStudiedCards);
      saveProgress(newStudiedCards);

      // Slide animation
      Animated.timing(slideAnim, {
        toValue: -screenWidth,
        duration: 300,
        useNativeDriver: true,
      }).start(() => {
        setCurrentIndex(currentIndex + 1);
        setIsFlipped(false);
        flipAnim.setValue(0);
        slideAnim.setValue(screenWidth);
        Animated.timing(slideAnim, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        }).start();
      });
    } else {
      // End of deck
      Alert.alert(
        'Deck Complete!',
        `You've studied ${studiedCards.size + 1} out of ${flashcards.length} cards.`,
        [
          { text: 'Restart', onPress: restartDeck },
          { text: 'Back to Tools', onPress: () => navigation.goBack() }
        ]
      );
    }
  };

  const previousCard = () => {
    if (currentIndex > 0) {
      Animated.timing(slideAnim, {
        toValue: screenWidth,
        duration: 300,
        useNativeDriver: true,
      }).start(() => {
        setCurrentIndex(currentIndex - 1);
        setIsFlipped(false);
        flipAnim.setValue(0);
        slideAnim.setValue(-screenWidth);
        Animated.timing(slideAnim, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        }).start();
      });
    }
  };

  const restartDeck = () => {
    setCurrentIndex(0);
    setIsFlipped(false);
    setStudiedCards(new Set());
    flipAnim.setValue(0);
    slideAnim.setValue(0);
    saveProgress(new Set());
  };

  const frontInterpolate = flipAnim.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '180deg'],
  });

  const backInterpolate = flipAnim.interpolate({
    inputRange: [0, 1],
    outputRange: ['180deg', '360deg'],
  });

  if (flashcards.length === 0) {
    return (
      <View style={[styles.container, styles.centerContent]}>
        <Text style={[styles.emptyText, { color: darkMode ? '#FFFFFF' : '#333333' }]}>
          No flashcards available for this difficulty level.
        </Text>
      </View>
    );
  }

  const currentCard = flashcards[currentIndex];

  return (
    <View style={[styles.container, darkMode ? styles.darkContainer : styles.lightContainer]}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={[
          styles.headerTitle,
          {
            color: darkMode ? '#FFFFFF' : '#333333',
            fontSize: 24 * fontSizeMultiplier
          }
        ]}>
          Flashcards - {category}
        </Text>
        <Text style={[
          styles.progressText,
          {
            color: darkMode ? '#CCCCCC' : '#666666',
            fontSize: 16 * fontSizeMultiplier
          }
        ]}>
          {currentIndex + 1} of {flashcards.length}
        </Text>
      </View>

      {/* Difficulty Selector */}
      <View style={styles.difficultyContainer}>
        {['easy', 'medium', 'hard'].map((level) => (
          <TouchableOpacity
            key={level}
            style={[
              styles.difficultyButton,
              difficulty === level && styles.activeDifficultyButton
            ]}
            onPress={() => setDifficulty(level)}
          >
            <Text style={[
              styles.difficultyText,
              difficulty === level && styles.activeDifficultyText
            ]}>
              {level.charAt(0).toUpperCase() + level.slice(1)}
            </Text>
          </TouchableOpacity>
        ))}
      </View>

      {/* Flashcard */}
      <View style={styles.cardContainer}>
        <Animated.View style={[
          styles.card,
          { transform: [{ translateX: slideAnim }] }
        ]}>
          {/* Front of card */}
          <Animated.View style={[
            styles.cardFace,
            styles.cardFront,
            { transform: [{ rotateY: frontInterpolate }] }
          ]}>
            <LinearGradient
              colors={['#667eea', '#764ba2']}
              style={styles.cardGradient}
            >
              <Text style={[styles.cardLabel, { fontSize: 14 * fontSizeMultiplier }]}>
                QUESTION
              </Text>
              <Text style={[styles.cardText, { fontSize: 18 * fontSizeMultiplier }]}>
                {currentCard.question}
              </Text>
              <TouchableOpacity style={styles.flipHint} onPress={flipCard}>
                <Text style={[styles.flipHintText, { fontSize: 12 * fontSizeMultiplier }]}>
                  Tap to reveal answer
                </Text>
              </TouchableOpacity>
            </LinearGradient>
          </Animated.View>

          {/* Back of card */}
          <Animated.View style={[
            styles.cardFace,
            styles.cardBack,
            { transform: [{ rotateY: backInterpolate }] }
          ]}>
            <LinearGradient
              colors={['#f093fb', '#f5576c']}
              style={styles.cardGradient}
            >
              <Text style={[styles.cardLabel, { fontSize: 14 * fontSizeMultiplier }]}>
                ANSWER
              </Text>
              <Text style={[styles.cardText, { fontSize: 16 * fontSizeMultiplier }]}>
                {currentCard.answer}
              </Text>
            </LinearGradient>
          </Animated.View>
        </Animated.View>
      </View>

      {/* Controls */}
      <View style={styles.controls}>
        <TouchableOpacity
          style={[styles.controlButton, currentIndex === 0 && styles.disabledButton]}
          onPress={previousCard}
          disabled={currentIndex === 0}
        >
          <Ionicons name="chevron-back" size={24} color="#FFFFFF" />
        </TouchableOpacity>

        <TouchableOpacity style={styles.flipButton} onPress={flipCard}>
          <Ionicons name="refresh" size={24} color="#FFFFFF" />
          <Text style={[styles.flipButtonText, { fontSize: 14 * fontSizeMultiplier }]}>
            Flip
          </Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.controlButton} onPress={nextCard}>
          <Ionicons name="chevron-forward" size={24} color="#FFFFFF" />
        </TouchableOpacity>
      </View>

      {/* Banner Ad */}
      <View style={styles.adContainer}>
        <BannerAdComponent
          placement={AD_PLACEMENTS.SETTINGS_BANNER}
          fallbackToWebView={true}
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  darkContainer: {
    backgroundColor: '#121212',
  },
  lightContainer: {
    backgroundColor: '#F5F5F5',
  },
  centerContent: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  header: {
    padding: 20,
    paddingTop: 40,
    alignItems: 'center',
  },
  headerTitle: {
    fontWeight: 'bold',
    marginBottom: 8,
  },
  progressText: {
    fontWeight: '500',
  },
  difficultyContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    paddingHorizontal: 20,
    marginBottom: 20,
  },
  difficultyButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    marginHorizontal: 4,
    borderRadius: 20,
    backgroundColor: '#E0E0E0',
  },
  activeDifficultyButton: {
    backgroundColor: '#667eea',
  },
  difficultyText: {
    color: '#666666',
    fontWeight: '500',
  },
  activeDifficultyText: {
    color: '#FFFFFF',
  },
  cardContainer: {
    flex: 1,
    justifyContent: 'center',
    paddingHorizontal: 20,
  },
  card: {
    height: 300,
    position: 'relative',
  },
  cardFace: {
    position: 'absolute',
    width: '100%',
    height: '100%',
    backfaceVisibility: 'hidden',
    borderRadius: 16,
    overflow: 'hidden',
  },
  cardFront: {
    // Front face styles
  },
  cardBack: {
    // Back face styles
  },
  cardGradient: {
    flex: 1,
    padding: 24,
    justifyContent: 'center',
    alignItems: 'center',
  },
  cardLabel: {
    color: '#FFFFFF',
    fontWeight: 'bold',
    opacity: 0.8,
    marginBottom: 16,
    letterSpacing: 1,
  },
  cardText: {
    color: '#FFFFFF',
    textAlign: 'center',
    lineHeight: 24,
    flex: 1,
    textAlignVertical: 'center',
  },
  flipHint: {
    marginTop: 16,
    paddingVertical: 8,
    paddingHorizontal: 16,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 20,
  },
  flipHintText: {
    color: '#FFFFFF',
    opacity: 0.9,
  },
  controls: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
    paddingHorizontal: 40,
    paddingVertical: 20,
  },
  controlButton: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: '#667eea',
    justifyContent: 'center',
    alignItems: 'center',
  },
  disabledButton: {
    backgroundColor: '#CCCCCC',
  },
  flipButton: {
    paddingHorizontal: 24,
    paddingVertical: 12,
    backgroundColor: '#f093fb',
    borderRadius: 25,
    flexDirection: 'row',
    alignItems: 'center',
  },
  flipButtonText: {
    color: '#FFFFFF',
    marginLeft: 8,
    fontWeight: '500',
  },
  emptyText: {
    fontSize: 18,
    textAlign: 'center',
    marginHorizontal: 40,
  },
  adContainer: {
    width: '100%',
    alignItems: 'center',
    backgroundColor: 'transparent',
  },
});

export default FlashcardsScreen;
