/**
 * This code was generated by [React Native](https://www.npmjs.com/package/@react-native/gradle-plugin).
 *
 * Do not edit this file as changes may cause incorrect behavior and will be lost
 * once the code is regenerated.
 *
 */

#include "autolinking.h"
#include <rnasyncstorage.h>
#include <rnclipboard.h>
#include <RNCSlider.h>
#include <react/renderer/components/RNCSlider/ComponentDescriptors.h>
#include <rnpicker.h>
#include <react/renderer/components/rnpicker/ComponentDescriptors.h>
#include <rngesturehandler_codegen.h>
#include <react/renderer/components/rngesturehandler_codegen/ComponentDescriptors.h>
#include <RNGoogleMobileAdsSpec.h>
#include <react/renderer/components/RNGoogleMobileAdsSpec/ComponentDescriptors.h>
#include <RNHapticFeedbackSpec.h>
#include <rnpdf.h>
#include <react/renderer/components/rnpdf/ComponentDescriptors.h>
#include <rnreanimated.h>
#include <safeareacontext.h>
#include <react/renderer/components/safeareacontext/ComponentDescriptors.h>
#include <rnscreens.h>
#include <react/renderer/components/rnscreens/ComponentDescriptors.h>
#include <RNVectorIconsSpec.h>
#include <RNCWebViewSpec.h>
#include <react/renderer/components/RNCWebViewSpec/ComponentDescriptors.h>

namespace facebook {
namespace react {

std::shared_ptr<TurboModule> autolinking_ModuleProvider(const std::string moduleName, const JavaTurboModule::InitParams &params) {
auto module_rnasyncstorage = rnasyncstorage_ModuleProvider(moduleName, params);
if (module_rnasyncstorage != nullptr) {
return module_rnasyncstorage;
}
auto module_rnclipboard = rnclipboard_ModuleProvider(moduleName, params);
if (module_rnclipboard != nullptr) {
return module_rnclipboard;
}
auto module_RNCSlider = RNCSlider_ModuleProvider(moduleName, params);
if (module_RNCSlider != nullptr) {
return module_RNCSlider;
}
auto module_rnpicker = rnpicker_ModuleProvider(moduleName, params);
if (module_rnpicker != nullptr) {
return module_rnpicker;
}
auto module_rngesturehandler_codegen = rngesturehandler_codegen_ModuleProvider(moduleName, params);
if (module_rngesturehandler_codegen != nullptr) {
return module_rngesturehandler_codegen;
}
auto module_RNGoogleMobileAdsSpec = RNGoogleMobileAdsSpec_ModuleProvider(moduleName, params);
if (module_RNGoogleMobileAdsSpec != nullptr) {
return module_RNGoogleMobileAdsSpec;
}
auto module_RNHapticFeedbackSpec = RNHapticFeedbackSpec_ModuleProvider(moduleName, params);
if (module_RNHapticFeedbackSpec != nullptr) {
return module_RNHapticFeedbackSpec;
}
auto module_rnpdf = rnpdf_ModuleProvider(moduleName, params);
if (module_rnpdf != nullptr) {
return module_rnpdf;
}
auto module_rnreanimated = rnreanimated_ModuleProvider(moduleName, params);
if (module_rnreanimated != nullptr) {
return module_rnreanimated;
}
auto module_safeareacontext = safeareacontext_ModuleProvider(moduleName, params);
if (module_safeareacontext != nullptr) {
return module_safeareacontext;
}
auto module_rnscreens = rnscreens_ModuleProvider(moduleName, params);
if (module_rnscreens != nullptr) {
return module_rnscreens;
}
auto module_RNVectorIconsSpec = RNVectorIconsSpec_ModuleProvider(moduleName, params);
if (module_RNVectorIconsSpec != nullptr) {
return module_RNVectorIconsSpec;
}
auto module_RNCWebViewSpec = RNCWebViewSpec_ModuleProvider(moduleName, params);
if (module_RNCWebViewSpec != nullptr) {
return module_RNCWebViewSpec;
}
  return nullptr;
}

std::shared_ptr<TurboModule> autolinking_cxxModuleProvider(const std::string moduleName, const std::shared_ptr<CallInvoker>& jsInvoker) {

  return nullptr;
}

void autolinking_registerProviders(std::shared_ptr<ComponentDescriptorProviderRegistry const> providerRegistry) {
providerRegistry->add(concreteComponentDescriptorProvider<RNCSliderComponentDescriptor>());
providerRegistry->add(concreteComponentDescriptorProvider<RNCAndroidDialogPickerComponentDescriptor>());
providerRegistry->add(concreteComponentDescriptorProvider<RNCAndroidDropdownPickerComponentDescriptor>());
providerRegistry->add(concreteComponentDescriptorProvider<RNGestureHandlerButtonComponentDescriptor>());
providerRegistry->add(concreteComponentDescriptorProvider<RNGestureHandlerRootViewComponentDescriptor>());
providerRegistry->add(concreteComponentDescriptorProvider<RNGoogleMobileAdsBannerViewComponentDescriptor>());
providerRegistry->add(concreteComponentDescriptorProvider<RNGoogleMobileAdsMediaViewComponentDescriptor>());
providerRegistry->add(concreteComponentDescriptorProvider<RNGoogleMobileAdsNativeViewComponentDescriptor>());
providerRegistry->add(concreteComponentDescriptorProvider<RNPDFPdfViewComponentDescriptor>());
providerRegistry->add(concreteComponentDescriptorProvider<RNCSafeAreaProviderComponentDescriptor>());
providerRegistry->add(concreteComponentDescriptorProvider<RNCSafeAreaViewComponentDescriptor>());
providerRegistry->add(concreteComponentDescriptorProvider<RNSFullWindowOverlayComponentDescriptor>());
providerRegistry->add(concreteComponentDescriptorProvider<RNSScreenContainerComponentDescriptor>());
providerRegistry->add(concreteComponentDescriptorProvider<RNSScreenNavigationContainerComponentDescriptor>());
providerRegistry->add(concreteComponentDescriptorProvider<RNSScreenStackHeaderConfigComponentDescriptor>());
providerRegistry->add(concreteComponentDescriptorProvider<RNSScreenStackHeaderSubviewComponentDescriptor>());
providerRegistry->add(concreteComponentDescriptorProvider<RNSScreenStackComponentDescriptor>());
providerRegistry->add(concreteComponentDescriptorProvider<RNSSearchBarComponentDescriptor>());
providerRegistry->add(concreteComponentDescriptorProvider<RNSScreenComponentDescriptor>());
providerRegistry->add(concreteComponentDescriptorProvider<RNSScreenFooterComponentDescriptor>());
providerRegistry->add(concreteComponentDescriptorProvider<RNSScreenContentWrapperComponentDescriptor>());
providerRegistry->add(concreteComponentDescriptorProvider<RNSModalScreenComponentDescriptor>());
providerRegistry->add(concreteComponentDescriptorProvider<RNCWebViewComponentDescriptor>());
  return;
}

} // namespace react
} // namespace facebook