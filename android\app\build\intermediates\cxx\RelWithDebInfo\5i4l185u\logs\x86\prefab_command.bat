@echo off
"C:\\Program Files\\Eclipse Adoptium\\jdk-********-hotspot\\bin\\java" ^
  --class-path ^
  "C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\com.google.prefab\\cli\\2.1.0\\aa32fec809c44fa531f01dcfb739b5b3304d3050\\cli-2.1.0-all.jar" ^
  com.google.prefab.cli.AppKt ^
  --build-system ^
  cmake ^
  --platform ^
  android ^
  --abi ^
  x86 ^
  --os-version ^
  24 ^
  --stl ^
  c++_shared ^
  --ndk-version ^
  26 ^
  --output ^
  "C:\\Users\\<USER>\\AppData\\Local\\Temp\\agp-prefab-staging7069774923929298131\\staged-cli-output" ^
  "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\483e65f44ee89b5f84e15be5bfd4ccf4\\transformed\\react-android-0.76.9-release\\prefab" ^
  "C:\\Users\\<USER>\\quiz-bee-techs\\android\\app\\build\\intermediates\\cxx\\refs\\react-native-reanimated\\2rm715uq" ^
  "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f58bbd842d9fa1ba150e62d63d89de04\\transformed\\hermes-android-0.76.9-release\\prefab" ^
  "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\6c49d09eb192753345178d717e7f7cc5\\transformed\\fbjni-0.6.0\\prefab"
