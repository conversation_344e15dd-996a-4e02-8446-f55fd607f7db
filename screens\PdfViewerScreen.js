import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { StyleSheet, Dimensions, View, ActivityIndicator, Text, TouchableOpacity, Alert, Linking, Platform } from 'react-native';
import { useRoute, useNavigation } from '@react-navigation/native';
import { WebView } from 'react-native-webview';
import { Ionicons } from '@expo/vector-icons';
import * as Sharing from 'expo-sharing';
import * as FileSystem from 'expo-file-system';

// Try to import react-native-pdf with fallback
let Pdf = null;
try {
  Pdf = require('react-native-pdf').default;
  console.log('react-native-pdf loaded successfully');
} catch (_error) {
  console.log('react-native-pdf not available, using WebView fallback');
}

const PdfViewerScreen = () => {
    const route = useRoute();
    const navigation = useNavigation();
    const [useWebView, setUseWebView] = useState(!Pdf); // Use native PDF if available, otherwise WebView
    const [loading, setLoading] = useState(true);
    const [currentViewerIndex, setCurrentViewerIndex] = useState(0);
    const [retryCount, setRetryCount] = useState(0);
    const [error, setError] = useState(null);

    // Expecting the PDF source URI to be passed as a route parameter
    const { sourceUri } = route.params || {};

    // Check if this is a local file or remote URL
    const isLocalFile = sourceUri && (sourceUri.startsWith('file://') || sourceUri.includes('DocumentDirectory') || sourceUri.includes('cacheDirectory'));

    // Try multiple PDF viewing approaches for better compatibility
    const viewerOptions = useMemo(() => {
        if (isLocalFile) {
            // For local files, only return the direct URI
            return [sourceUri];
        } else {
            // For remote files, try multiple viewers with better encoding
            const encodedUri = encodeURIComponent(sourceUri);
            return [
                sourceUri, // Direct PDF URL first (works best for most PDFs)
                `https://mozilla.github.io/pdf.js/web/viewer.html?file=${encodedUri}`, // Mozilla PDF.js viewer
                `https://docs.google.com/gview?embedded=true&url=${encodedUri}`, // Google Docs viewer
                `https://drive.google.com/viewerng/viewer?embedded=true&url=${encodedUri}` // Google Drive viewer
            ];
        }
    }, [isLocalFile, sourceUri]);

    const handleWebViewError = useCallback((syntheticEvent) => {
        const { nativeEvent } = syntheticEvent;
        console.error('WebView PDF Error:', nativeEvent);
        console.error('Current PDF URL:', sourceUri);
        console.error('Current viewer option:', viewerOptions[currentViewerIndex]);

        // Try next viewer option if available
        if (currentViewerIndex < viewerOptions.length - 1) {
            console.log(`Trying alternative PDF viewer (${currentViewerIndex + 2}/${viewerOptions.length})`);
            setCurrentViewerIndex(currentViewerIndex + 1);
            setRetryCount(retryCount + 1);
            setLoading(true);
        } else {
            // All viewer options exhausted, show options
            setLoading(false);
            setError('Failed to load PDF in app');
            Alert.alert(
                'PDF Loading Error',
                'Unable to load PDF inside the app. Choose an option:',
                [
                    { text: 'Go Back', onPress: () => navigation.goBack() },
                    { text: 'Open Externally', onPress: () => openPdfExternally() },
                    { text: 'Retry', onPress: () => {
                        setCurrentViewerIndex(0);
                        setRetryCount(0);
                        setLoading(true);
                        setError(null);
                    }}
                ]
            );
        }
    }, [currentViewerIndex, viewerOptions, retryCount, sourceUri, navigation]);

    const openPdfExternally = async () => {
        try {
            if (Platform.OS === 'android') {
                // For Android, try to open with intent
                const canOpen = await Linking.canOpenURL(sourceUri);
                if (canOpen) {
                    await Linking.openURL(sourceUri);
                } else {
                    // Fallback: try to share the PDF
                    if (await Sharing.isAvailableAsync()) {
                        await Sharing.shareAsync(sourceUri);
                    } else {
                        Alert.alert(
                            'Cannot Open PDF',
                            'Please install a PDF viewer app from the Play Store.'
                        );
                    }
                }
            } else {
                // For iOS
                await Linking.openURL(sourceUri);
            }
        } catch (error) {
            console.error('Error opening PDF externally:', error);
            Alert.alert('Error', 'Could not open PDF externally.');
        }
    };

    // Add timeout for loading and URL validation
    useEffect(() => {
        console.log('PDF Viewer initialized with sourceUri:', sourceUri);
        console.log('Is local file:', isLocalFile);
        console.log('Native PDF available:', !!Pdf);

        // Reset states when viewer option changes
        setLoading(true);
        setError(null);

        // For local files, prefer native PDF viewer
        if (isLocalFile) {
            if (Pdf) {
                console.log('Using native PDF viewer for local file');
                setUseWebView(false);
            } else {
                console.warn('Local PDF file detected but native PDF viewer not available');
                // For local files without native PDF, try to convert to data URL or share
                setUseWebView(true);
            }
        } else {
            // For remote files, validate URL accessibility first
            const validateUrl = async () => {
                try {
                    console.log('Validating PDF URL:', sourceUri);

                    // Simple fetch without AbortController for better compatibility
                    const response = await Promise.race([
                        fetch(sourceUri, { method: 'HEAD' }),
                        new Promise((_, reject) =>
                            setTimeout(() => reject(new Error('Timeout')), 10000)
                        )
                    ]);

                    console.log('URL validation response:', response.status, response.statusText);
                    if (!response.ok) {
                        console.error('PDF URL not accessible:', response.status, response.statusText);
                        // Don't immediately fail - let the viewer try anyway
                        console.log('Proceeding with PDF loading despite validation failure');
                    }
                } catch (error) {
                    console.error('URL validation failed:', error);
                    // Don't immediately fail - let the viewer try anyway
                    console.log('Proceeding with PDF loading despite validation failure');
                }
            };

            validateUrl();
        }

        // Set a timeout to prevent infinite loading
        const loadingTimeout = setTimeout(() => {
            if (loading) {
                console.log('PDF loading timeout reached');
                handleWebViewError({ nativeEvent: { description: 'Loading timeout (20s)' } });
            }
        }, 20000); // 20 second timeout

        return () => clearTimeout(loadingTimeout);
    }, [currentViewerIndex, sourceUri, isLocalFile]);

    if (!sourceUri) {
        return (
            <View style={styles.container}>
                <View style={styles.header}>
                    <TouchableOpacity onPress={() => navigation.goBack()} style={styles.backButton}>
                        <Ionicons name="arrow-back" size={24} color="#333" />
                    </TouchableOpacity>
                    <Text style={styles.title}>PDF Viewer</Text>
                </View>
                <View style={styles.errorContainer}>
                    <Text style={styles.errorText}>Error: No PDF source provided.</Text>
                </View>
            </View>
        );
    }

    // Always try to display PDF inside the app first
    // Only show external app option as a last resort if both native PDF and WebView fail

    const handleNativePdfError = (error) => {
        console.error('Native PDF Error:', error);
        console.log('Automatically switching to WebView fallback');
        setUseWebView(true); // Automatically switch to WebView instead of showing alert
        setLoading(false);
    };

    const renderNativePdf = () => (
        <Pdf
            trustAllCerts={false}
            source={{ uri: sourceUri, cache: true }}
            onLoadComplete={(numberOfPages) => {
                console.log(`PDF loaded: ${numberOfPages} pages`);
                setLoading(false);
                setError(null);
            }}
            onPageChanged={(page, numberOfPages) => {
                console.log(`Current page: ${page} of ${numberOfPages}`);
            }}
            onError={handleNativePdfError}
            onPressLink={(uri) => {
                console.log(`Link pressed: ${uri}`);
                Linking.openURL(uri);
            }}
            style={styles.pdf}
            renderActivityIndicator={() => (
                <View style={styles.loadingContainer}>
                    <ActivityIndicator color="blue" size="large" />
                    <Text style={styles.loadingText}>Loading PDF...</Text>
                </View>
            )}
            enablePaging={true}
            enableRTL={false}
            enableDoubleTapZoom={true}
            enableAnnotationRendering={true}
            password=""
            spacing={10}
            horizontal={false}
        />
    );



    const renderWebViewPdf = () => {
        console.log(`Rendering WebView with viewer option ${currentViewerIndex}: ${viewerOptions[currentViewerIndex]}`);

        return (
            <WebView
                source={{ uri: viewerOptions[currentViewerIndex] }}
                style={styles.pdf}
                onLoadStart={() => {
                    console.log('WebView load started');
                    setLoading(true);
                }}
                onLoadEnd={() => {
                    console.log('WebView load ended');
                    setLoading(false);
                }}
                onError={handleWebViewError}
                startInLoadingState={true}
                renderLoading={() => (
                    <View style={styles.loadingContainer}>
                        <ActivityIndicator color="blue" size="large" />
                        <Text style={styles.loadingText}>Loading PDF...</Text>
                        {retryCount > 0 && (
                            <Text style={styles.retryText}>
                                Trying alternative viewer ({currentViewerIndex + 1}/{viewerOptions.length})
                            </Text>
                        )}
                    </View>
                )}
                // Additional props for better PDF handling
                allowsInlineMediaPlayback={true}
                mediaPlaybackRequiresUserAction={false}
                javaScriptEnabled={true}
                domStorageEnabled={true}
                allowFileAccess={true}
                allowUniversalAccessFromFileURLs={true}
                mixedContentMode="compatibility"
                onHttpError={(syntheticEvent) => {
                    const { nativeEvent } = syntheticEvent;
                    console.error('HTTP Error:', nativeEvent);
                    handleWebViewError(syntheticEvent);
                }}
            />
        );
    };

    return (
        <View style={styles.container}>
            <View style={styles.header}>
                <TouchableOpacity onPress={() => navigation.goBack()} style={styles.backButton}>
                    <Ionicons name="arrow-back" size={24} color="#333" />
                </TouchableOpacity>
                <Text style={styles.title}>PDF Viewer</Text>
                {Pdf && (
                    <TouchableOpacity
                        onPress={() => setUseWebView(!useWebView)}
                        style={styles.toggleButton}
                    >
                        <Ionicons
                            name={useWebView ? "document" : "globe"}
                            size={20}
                            color="#007AFF"
                        />
                    </TouchableOpacity>
                )}
            </View>

            {loading && (
                <View style={styles.loadingContainer}>
                    <ActivityIndicator color="blue" size="large" />
                    <Text style={styles.loadingText}>Loading PDF...</Text>
                </View>
            )}

            {useWebView ? renderWebViewPdf() : renderNativePdf()}
        </View>
    );
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#fff',
    },
    header: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        paddingHorizontal: 16,
        paddingVertical: 12,
        backgroundColor: '#f8f9fa',
        borderBottomWidth: 1,
        borderBottomColor: '#e9ecef',
    },
    backButton: {
        padding: 8,
    },
    title: {
        fontSize: 18,
        fontWeight: 'bold',
        flex: 1,
        textAlign: 'center',
        marginHorizontal: 16,
    },
    toggleButton: {
        padding: 8,
    },
    pdf: {
        flex: 1,
        width: Dimensions.get('window').width,
        height: Dimensions.get('window').height,
    },
    errorContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        padding: 20,
    },
    errorText: {
        fontSize: 18,
        color: 'red',
        textAlign: 'center',
    },
    loadingContainer: {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: 'rgba(255, 255, 255, 0.8)',
        zIndex: 1000,
    },
    loadingText: {
        marginTop: 10,
        fontSize: 16,
        color: '#333',
    },
    retryText: {
        marginTop: 5,
        fontSize: 14,
        color: '#666',
        fontStyle: 'italic',
    },
    errorSubText: {
        fontSize: 14,
        color: '#666',
        textAlign: 'center',
        marginTop: 10,
        paddingHorizontal: 20,
        lineHeight: 20,
    },
    retryButton: {
        backgroundColor: '#007AFF',
        paddingHorizontal: 20,
        paddingVertical: 10,
        borderRadius: 8,
        marginTop: 20,
    },
    retryButtonText: {
        color: '#fff',
        fontSize: 16,
        fontWeight: '600',
    },
});

export default PdfViewerScreen;
