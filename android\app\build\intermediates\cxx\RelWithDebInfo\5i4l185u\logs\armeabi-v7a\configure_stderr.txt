CMake Warning in C:/Users/<USER>/quiz-bee-techs/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/CMakeLists.txt:
  The object file directory

    C:/Users/<USER>/quiz-bee-techs/android/app/.cxx/RelWithDebInfo/5i4l185u/armeabi-v7a/rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/./

  has 181 characters.  The maximum full path to an object file is 250
  characters (see CMAKE_OBJECT_PATH_MAX).  Object file

    react/renderer/components/rngesturehandler_codegen/rngesturehandler_codegenJSI-generated.cpp.o

  cannot be safely placed under this directory.  The build may not work
  correctly.


