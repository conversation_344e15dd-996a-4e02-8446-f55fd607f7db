<?xml version="1.0" encoding="utf-8"?>
<resources>
    <color name="colorPrimary">#023c69</color>
    <color name="colorPrimaryDark">#ffffff</color>
    <color name="iconBackground">#ffffff</color>
    <color name="splashscreen_background">#ffffff</color>
    <integer name="react_native_dev_server_port">8081</integer>
    <string name="app_name">bee teach</string>
    <string name="expo_splash_screen_resize_mode" translatable="false">contain</string>
    <string name="expo_splash_screen_status_bar_translucent" translatable="false">false</string>
    <string name="expo_system_ui_user_interface_style" translatable="false">light</string>
    <style name="AppTheme" parent="Theme.AppCompat.Light.NoActionBar">
    <item name="android:textColor">@android:color/black</item>
    <item name="android:editTextStyle">@style/ResetEditText</item>
    <item name="android:editTextBackground">@drawable/rn_edit_text_material</item>
    <item name="colorPrimary">@color/colorPrimary</item>
    <item name="android:statusBarColor">#ffffff</item>
  </style>
    <style name="ResetEditText" parent="@android:style/Widget.EditText">
    <item name="android:padding">0dp</item>
    <item name="android:textColorHint">#c8c8c8</item>
    <item name="android:textColor">@android:color/black</item>
  </style>
    <style name="Theme.App.SplashScreen" parent="Theme.SplashScreen">
    <item name="windowSplashScreenBackground">@color/splashscreen_background</item>
    <item name="windowSplashScreenAnimatedIcon">@drawable/splashscreen_logo</item>
    <item name="postSplashScreenTheme">@style/AppTheme</item>
  </style>
</resources>