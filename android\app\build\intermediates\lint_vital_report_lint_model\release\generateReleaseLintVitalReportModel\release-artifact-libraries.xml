<libraries>
  <library
      name=":@@:expo::release"
      project=":expo"/>
  <library
      name=":@@:react-native-google-mobile-ads::release"
      project=":react-native-google-mobile-ads"/>
  <library
      name=":@@:react-native-screens::release"
      project=":react-native-screens"/>
  <library
      name=":@@:react-native-community_slider::release"
      project=":react-native-community_slider"/>
  <library
      name="com.facebook.react:react-android:0.76.9:release@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\483e65f44ee89b5f84e15be5bfd4ccf4\transformed\react-android-0.76.9-release\jars\classes.jar"
      resolved="com.facebook.react:react-android:0.76.9"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\483e65f44ee89b5f84e15be5bfd4ccf4\transformed\react-android-0.76.9-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:expo-av::release"
      project=":expo-av"/>
  <library
      name=":@@:expo-file-system::release"
      project=":expo-file-system"/>
  <library
      name="com.facebook.fresco:imagepipeline-okhttp3:3.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c1791b537cd0724937f979305af4275c\transformed\imagepipeline-okhttp3-3.2.0\jars\classes.jar"
      resolved="com.facebook.fresco:imagepipeline-okhttp3:3.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c1791b537cd0724937f979305af4275c\transformed\imagepipeline-okhttp3-3.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.squareup.okhttp3:okhttp-urlconnection:4.9.2@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.squareup.okhttp3\okhttp-urlconnection\4.9.2\3b9e64d3d56370bc7488ed8b336d17a8013cb336\okhttp-urlconnection-4.9.2.jar"
      resolved="com.squareup.okhttp3:okhttp-urlconnection:4.9.2"/>
  <library
      name="com.google.android.exoplayer:extension-okhttp:2.18.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5e50dd07750c23dd304136458a0074d4\transformed\extension-okhttp-2.18.1\jars\classes.jar"
      resolved="com.google.android.exoplayer:extension-okhttp:2.18.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5e50dd07750c23dd304136458a0074d4\transformed\extension-okhttp-2.18.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.squareup.okhttp3:okhttp:4.9.2@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.squareup.okhttp3\okhttp\4.9.2\5302714ee9320b64cf65ed865e5f65981ef9ba46\okhttp-4.9.2.jar"
      resolved="com.squareup.okhttp3:okhttp:4.9.2"/>
  <library
      name="com.squareup.okio:okio:2.9.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.squareup.okio\okio\2.9.0\dcc813b08ce5933f8bdfd1dfbab4ad4bd170e7a\okio-jvm-2.9.0.jar"
      resolved="com.squareup.okio:okio:2.9.0"/>
  <library
      name=":@@:expo-modules-core::release"
      project=":expo-modules-core"/>
  <library
      name=":@@:expo-dev-launcher::release"
      project=":expo-dev-launcher"/>
  <library
      name=":@@:expo-dev-menu::release"
      project=":expo-dev-menu"/>
  <library
      name="androidx.lifecycle:lifecycle-extensions:2.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\43902973ef4e55b93063a72897459078\transformed\lifecycle-extensions-2.2.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-extensions:2.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\43902973ef4e55b93063a72897459078\transformed\lifecycle-extensions-2.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.appcompat:appcompat-resources:1.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6f210015213da647e492d5a81da7ae5b\transformed\appcompat-resources-1.7.0\jars\classes.jar"
      resolved="androidx.appcompat:appcompat-resources:1.7.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6f210015213da647e492d5a81da7ae5b\transformed\appcompat-resources-1.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.appcompat:appcompat:1.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\68cda876f16b6c181c93767ccf9edd36\transformed\appcompat-1.7.0\jars\classes.jar"
      resolved="androidx.appcompat:appcompat:1.7.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\68cda876f16b6c181c93767ccf9edd36\transformed\appcompat-1.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:expo-device::release"
      project=":expo-device"/>
  <library
      name=":@@:expo-sharing::release"
      project=":expo-sharing"/>
  <library
      name="androidx.legacy:legacy-support-v4:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83ead58e580dbeb71d2f972d63b886a9\transformed\legacy-support-v4-1.0.0\jars\classes.jar"
      resolved="androidx.legacy:legacy-support-v4:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83ead58e580dbeb71d2f972d63b886a9\transformed\legacy-support-v4-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:expo-image-loader::release"
      project=":expo-image-loader"/>
  <library
      name="com.github.bumptech.glide:glide:4.16.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cb4bf9c2b5297c0ad5f5a4e40051eeee\transformed\glide-4.16.0\jars\classes.jar"
      resolved="com.github.bumptech.glide:glide:4.16.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cb4bf9c2b5297c0ad5f5a4e40051eeee\transformed\glide-4.16.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.ump:user-messaging-platform:3.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\77e3c456080d42ee270cf449a499429b\transformed\user-messaging-platform-3.1.0\jars\classes.jar"
      resolved="com.google.android.ump:user-messaging-platform:3.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\77e3c456080d42ee270cf449a499429b\transformed\user-messaging-platform-3.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-basement:18.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\02b86cfeeba2fc323c383047854b9491\transformed\play-services-basement-18.2.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-basement:18.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\02b86cfeeba2fc323c383047854b9491\transformed\play-services-basement-18.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.fragment:fragment:1.6.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\14023815bb5ea6499e14566fe942423f\transformed\fragment-1.6.1\jars\classes.jar"
      resolved="androidx.fragment:fragment:1.6.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\14023815bb5ea6499e14566fe942423f\transformed\fragment-1.6.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.activity:activity:1.7.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bd734e866277d79bc99cbd41cd3b5cc9\transformed\activity-1.7.2\jars\classes.jar"
      resolved="androidx.activity:activity:1.7.2"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bd734e866277d79bc99cbd41cd3b5cc9\transformed\activity-1.7.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.legacy:legacy-support-core-ui:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1cc1e82617b45365f34f9842cb8a9e67\transformed\legacy-support-core-ui-1.0.0\jars\classes.jar"
      resolved="androidx.legacy:legacy-support-core-ui:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1cc1e82617b45365f34f9842cb8a9e67\transformed\legacy-support-core-ui-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.swiperefreshlayout:swiperefreshlayout:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f6d082d97706a7fc53ca300fb8bb334f\transformed\swiperefreshlayout-1.1.0\jars\classes.jar"
      resolved="androidx.swiperefreshlayout:swiperefreshlayout:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f6d082d97706a7fc53ca300fb8bb334f\transformed\swiperefreshlayout-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.core:core-ktx:1.13.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\650d5fd92dd7d63a22d00295210cebfb\transformed\core-ktx-1.13.1\jars\classes.jar"
      resolved="androidx.core:core-ktx:1.13.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\650d5fd92dd7d63a22d00295210cebfb\transformed\core-ktx-1.13.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.browser:browser:1.6.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\268d4161c8b8edb1216e3f661bb7ee02\transformed\browser-1.6.0\jars\classes.jar"
      resolved="androidx.browser:browser:1.6.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\268d4161c8b8edb1216e3f661bb7ee02\transformed\browser-1.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.drawerlayout:drawerlayout:1.1.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d89cabc7b27e151fdb8ea2aa0d3e6f77\transformed\drawerlayout-1.1.1\jars\classes.jar"
      resolved="androidx.drawerlayout:drawerlayout:1.1.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d89cabc7b27e151fdb8ea2aa0d3e6f77\transformed\drawerlayout-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.vectordrawable:vectordrawable-animated:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\73ce597354faf2df1e284b61b7b38336\transformed\vectordrawable-animated-1.1.0\jars\classes.jar"
      resolved="androidx.vectordrawable:vectordrawable-animated:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\73ce597354faf2df1e284b61b7b38336\transformed\vectordrawable-animated-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.vectordrawable:vectordrawable:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\82551a4ba64e189f67023068caf18ef5\transformed\vectordrawable-1.1.0\jars\classes.jar"
      resolved="androidx.vectordrawable:vectordrawable:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\82551a4ba64e189f67023068caf18ef5\transformed\vectordrawable-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.exoplayer:exoplayer:2.18.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e39524a91b0ed434a0cd3442c49a9429\transformed\exoplayer-2.18.1\jars\classes.jar"
      resolved="com.google.android.exoplayer:exoplayer:2.18.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e39524a91b0ed434a0cd3442c49a9429\transformed\exoplayer-2.18.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.exoplayer:exoplayer-ui:2.18.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f1013ccf4ee59567949af9e71a98b092\transformed\exoplayer-ui-2.18.1\jars\classes.jar"
      resolved="com.google.android.exoplayer:exoplayer-ui:2.18.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f1013ccf4ee59567949af9e71a98b092\transformed\exoplayer-ui-2.18.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.media:media:1.4.3@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32330779d42f886d84c661101fbf7e17\transformed\media-1.4.3\jars\classes.jar"
      resolved="androidx.media:media:1.4.3"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32330779d42f886d84c661101fbf7e17\transformed\media-1.4.3"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.legacy:legacy-support-core-utils:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e965e87a21b3075e804c706c3caa02b4\transformed\legacy-support-core-utils-1.0.0\jars\classes.jar"
      resolved="androidx.legacy:legacy-support-core-utils:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e965e87a21b3075e804c706c3caa02b4\transformed\legacy-support-core-utils-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.viewpager:viewpager:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b1d84e2e2d88c4d4f9d1b366c93c35e\transformed\viewpager-1.0.0\jars\classes.jar"
      resolved="androidx.viewpager:viewpager:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b1d84e2e2d88c4d4f9d1b366c93c35e\transformed\viewpager-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.coordinatorlayout:coordinatorlayout:1.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7807c2ccb5aa12592cfcf47bba99467d\transformed\coordinatorlayout-1.2.0\jars\classes.jar"
      resolved="androidx.coordinatorlayout:coordinatorlayout:1.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7807c2ccb5aa12592cfcf47bba99467d\transformed\coordinatorlayout-1.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.slidingpanelayout:slidingpanelayout:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2b4f6e9b43c9efe12efb4b6be6a60934\transformed\slidingpanelayout-1.0.0\jars\classes.jar"
      resolved="androidx.slidingpanelayout:slidingpanelayout:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2b4f6e9b43c9efe12efb4b6be6a60934\transformed\slidingpanelayout-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.customview:customview:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b07f68cfd0a94d2ad5a5c4cff567a32f\transformed\customview-1.1.0\jars\classes.jar"
      resolved="androidx.customview:customview:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b07f68cfd0a94d2ad5a5c4cff567a32f\transformed\customview-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.loader:loader:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9c12fa70ce3a19a2fff60cc3fedd7db0\transformed\loader-1.0.0\jars\classes.jar"
      resolved="androidx.loader:loader:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9c12fa70ce3a19a2fff60cc3fedd7db0\transformed\loader-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.asynclayoutinflater:asynclayoutinflater:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3eecf4c0e47c279c4e5d3ac740bbac76\transformed\asynclayoutinflater-1.0.0\jars\classes.jar"
      resolved="androidx.asynclayoutinflater:asynclayoutinflater:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3eecf4c0e47c279c4e5d3ac740bbac76\transformed\asynclayoutinflater-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.core:core:1.13.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71743d480ebb399fb4fe6b96265e7bca\transformed\core-1.13.1\jars\classes.jar"
      resolved="androidx.core:core:1.13.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71743d480ebb399fb4fe6b96265e7bca\transformed\core-1.13.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel:2.8.3@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ffba127a30d0c290bedd91c6fd089222\transformed\lifecycle-viewmodel-2.8.3\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel:2.8.3"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ffba127a30d0c290bedd91c6fd089222\transformed\lifecycle-viewmodel-2.8.3"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel-android:2.8.3@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a42fd3e3bf502eacac3603e60a35dc72\transformed\lifecycle-viewmodel-release\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel-android:2.8.3"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a42fd3e3bf502eacac3603e60a35dc72\transformed\lifecycle-viewmodel-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.3@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\348621d7bdd1258b37e0f566ee1a2acb\transformed\lifecycle-livedata-core-ktx-2.8.3\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.3"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\348621d7bdd1258b37e0f566ee1a2acb\transformed\lifecycle-livedata-core-ktx-2.8.3"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-common-jvm:2.8.3@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.lifecycle\lifecycle-common-jvm\2.8.3\7174a594afb73a9ad9ac9074ce78b94af3cc52a7\lifecycle-common-jvm-2.8.3.jar"
      resolved="androidx.lifecycle:lifecycle-common-jvm:2.8.3"/>
  <library
      name="androidx.lifecycle:lifecycle-runtime:2.8.3@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c3eb16a3b19de6c8983997819f117b26\transformed\lifecycle-runtime-2.8.3\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-runtime:2.8.3"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c3eb16a3b19de6c8983997819f117b26\transformed\lifecycle-runtime-2.8.3"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-runtime-android:2.8.3@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03a5235ac09c82de05a0b4ee4d6ae074\transformed\lifecycle-runtime-release\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-runtime-android:2.8.3"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03a5235ac09c82de05a0b4ee4d6ae074\transformed\lifecycle-runtime-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata-core:2.8.3@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\51321b78fdc8f8a0a2486c1af64e65a6\transformed\lifecycle-livedata-core-2.8.3\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata-core:2.8.3"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\51321b78fdc8f8a0a2486c1af64e65a6\transformed\lifecycle-livedata-core-2.8.3"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.3@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b6b15f902da55e70fbae62dfc64358e3\transformed\lifecycle-viewmodel-savedstate-2.8.3\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.3"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b6b15f902da55e70fbae62dfc64358e3\transformed\lifecycle-viewmodel-savedstate-2.8.3"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-service:2.8.3@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0b2132a622a2822c1d2831e29e63d543\transformed\lifecycle-service-2.8.3\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-service:2.8.3"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0b2132a622a2822c1d2831e29e63d543\transformed\lifecycle-service-2.8.3"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-process:2.8.3@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b3059939b2c8092c03a8dedf19cca0e7\transformed\lifecycle-process-2.8.3\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-process:2.8.3"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b3059939b2c8092c03a8dedf19cca0e7\transformed\lifecycle-process-2.8.3"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata:2.8.3@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e34e1b0c7d819b61f23f69793d2ef148\transformed\lifecycle-livedata-2.8.3\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata:2.8.3"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e34e1b0c7d819b61f23f69793d2ef148\transformed\lifecycle-livedata-2.8.3"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.8.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlinx\kotlinx-coroutines-core-jvm\1.8.1\bb0e192bd7c2b6b8217440d36e9758e377e450\kotlinx-coroutines-core-jvm-1.8.1.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.8.1"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.8.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlinx\kotlinx-coroutines-android\1.8.1\73e2acdd18df99dd4849d99f188dff529fc0afe0\kotlinx-coroutines-android-1.8.1.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.8.1"/>
  <library
      name="androidx.savedstate:savedstate:1.2.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4ea56063c39c63faa6734c60246e8cc6\transformed\savedstate-1.2.1\jars\classes.jar"
      resolved="androidx.savedstate:savedstate:1.2.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4ea56063c39c63faa6734c60246e8cc6\transformed\savedstate-1.2.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="io.insert-koin:koin-core-jvm:3.4.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\io.insert-koin\koin-core-jvm\3.4.0\6cc0aace8b12422cdb9e95b95ae7e2176cce1470\koin-core-jvm-3.4.0.jar"
      resolved="io.insert-koin:koin-core-jvm:3.4.0"/>
  <library
      name=":@@:expo-constants::release"
      project=":expo-constants"/>
  <library
      name=":@@:expo-haptics::release"
      project=":expo-haptics"/>
  <library
      name=":@@:expo-dev-client::release"
      project=":expo-dev-client"/>
  <library
      name="androidx.databinding:viewbinding:8.6.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d44b17e2d97d3467d4af625ecc50cdd6\transformed\viewbinding-8.6.0\jars\classes.jar"
      resolved="androidx.databinding:viewbinding:8.6.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d44b17e2d97d3467d4af625ecc50cdd6\transformed\viewbinding-8.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.cursoradapter:cursoradapter:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\25c1efd3cf18c9f6ecbf42d0dd30ffd6\transformed\cursoradapter-1.0.0\jars\classes.jar"
      resolved="androidx.cursoradapter:cursoradapter:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\25c1efd3cf18c9f6ecbf42d0dd30ffd6\transformed\cursoradapter-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.interpolator:interpolator:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\16d4c034bf36f10f1fa59a34ac5d340b\transformed\interpolator-1.0.0\jars\classes.jar"
      resolved="androidx.interpolator:interpolator:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\16d4c034bf36f10f1fa59a34ac5d340b\transformed\interpolator-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.arch.core:core-runtime:2.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\15c0bc55acf97df19f578c6f4970d7df\transformed\core-runtime-2.2.0\jars\classes.jar"
      resolved="androidx.arch.core:core-runtime:2.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\15c0bc55acf97df19f578c6f4970d7df\transformed\core-runtime-2.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.arch.core:core-common:2.2.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.arch.core\core-common\2.2.0\5e1b8b81dfd5f52c56a8d53b18ca759c19a301f3\core-common-2.2.0.jar"
      resolved="androidx.arch.core:core-common:2.2.0"/>
  <library
      name="com.github.bumptech.glide:gifdecoder:4.16.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2b3167d850800863f5bdc0c8cdaa2e05\transformed\gifdecoder-4.16.0\jars\classes.jar"
      resolved="com.github.bumptech.glide:gifdecoder:4.16.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2b3167d850800863f5bdc0c8cdaa2e05\transformed\gifdecoder-4.16.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.versionedparcelable:versionedparcelable:1.1.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c6f10c39003c2297002b9319f291b2e5\transformed\versionedparcelable-1.1.1\jars\classes.jar"
      resolved="androidx.versionedparcelable:versionedparcelable:1.1.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c6f10c39003c2297002b9319f291b2e5\transformed\versionedparcelable-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.documentfile:documentfile:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b2e94c53129cdd1d6fbfe2b8ae916788\transformed\documentfile-1.0.0\jars\classes.jar"
      resolved="androidx.documentfile:documentfile:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b2e94c53129cdd1d6fbfe2b8ae916788\transformed\documentfile-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.localbroadcastmanager:localbroadcastmanager:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\55fbab6651ea571a1aabdea203c5c5ce\transformed\localbroadcastmanager-1.0.0\jars\classes.jar"
      resolved="androidx.localbroadcastmanager:localbroadcastmanager:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\55fbab6651ea571a1aabdea203c5c5ce\transformed\localbroadcastmanager-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.print:print:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d2383594a3be8118551402390386bd93\transformed\print-1.0.0\jars\classes.jar"
      resolved="androidx.print:print:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d2383594a3be8118551402390386bd93\transformed\print-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.collection:collection-jvm:1.4.4@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.collection\collection-jvm\1.4.4\da13a7e557c430276b8cb490420effebc1398c0d\collection-jvm-1.4.4.jar"
      resolved="androidx.collection:collection-jvm:1.4.4"/>
  <library
      name="androidx.annotation:annotation-jvm:1.9.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.annotation\annotation-jvm\1.9.1\b17951747e38bf3986a24431b9ba0d039958aa5f\annotation-jvm-1.9.1.jar"
      resolved="androidx.annotation:annotation-jvm:1.9.1"/>
  <library
      name="androidx.annotation:annotation-experimental:1.4.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e632802597ddcae7768ef5a3e013ced2\transformed\annotation-experimental-1.4.1\jars\classes.jar"
      resolved="androidx.annotation:annotation-experimental:1.4.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e632802597ddcae7768ef5a3e013ced2\transformed\annotation-experimental-1.4.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib:2.0.21@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib\2.0.21\618b539767b4899b4660a83006e052b63f1db551\kotlin-stdlib-2.0.21.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib:2.0.21"/>
  <library
      name=":@@:react-native-async-storage_async-storage::release"
      project=":react-native-async-storage_async-storage"/>
  <library
      name=":@@:react-native-clipboard_clipboard::release"
      project=":react-native-clipboard_clipboard"/>
  <library
      name=":@@:react-native-picker_picker::release"
      project=":react-native-picker_picker"/>
  <library
      name=":@@:react-native-voice_voice::release"
      project=":react-native-voice_voice"/>
  <library
      name=":@@:react-native-fast-image::release"
      project=":react-native-fast-image"/>
  <library
      name=":@@:react-native-gesture-handler::release"
      project=":react-native-gesture-handler"/>
  <library
      name=":@@:react-native-haptic-feedback::release"
      project=":react-native-haptic-feedback"/>
  <library
      name=":@@:react-native-pdf::release"
      project=":react-native-pdf"/>
  <library
      name=":@@:react-native-reanimated::release"
      project=":react-native-reanimated"/>
  <library
      name=":@@:react-native-safe-area-context::release"
      project=":react-native-safe-area-context"/>
  <library
      name=":@@:react-native-vector-icons::release"
      project=":react-native-vector-icons"/>
  <library
      name=":@@:react-native-webview::release"
      project=":react-native-webview"/>
  <library
      name="androidx.multidex:multidex:2.0.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cb9f8b62e9cd38eb05b7aa2a9c14560f\transformed\multidex-2.0.1\jars\classes.jar"
      resolved="androidx.multidex:multidex:2.0.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cb9f8b62e9cd38eb05b7aa2a9c14560f\transformed\multidex-2.0.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:animated-gif:3.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3906aa7b2c02707ea467a93483aef413\transformed\animated-gif-3.2.0\jars\classes.jar"
      resolved="com.facebook.fresco:animated-gif:3.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3906aa7b2c02707ea467a93483aef413\transformed\animated-gif-3.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:webpsupport:3.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a0d22f0f20c7a063ddad9af33cd02e7e\transformed\webpsupport-3.2.0\jars\classes.jar"
      resolved="com.facebook.fresco:webpsupport:3.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a0d22f0f20c7a063ddad9af33cd02e7e\transformed\webpsupport-3.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.react:hermes-android:0.76.9:release@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f58bbd842d9fa1ba150e62d63d89de04\transformed\hermes-android-0.76.9-release\jars\classes.jar"
      resolved="com.facebook.react:hermes-android:0.76.9"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f58bbd842d9fa1ba150e62d63d89de04\transformed\hermes-android-0.76.9-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="org.jetbrains:annotations:23.0.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains\annotations\23.0.0\8cc20c07506ec18e0834947b84a864bfc094484e\annotations-23.0.0.jar"
      resolved="org.jetbrains:annotations:23.0.0"/>
  <library
      name="com.google.android.exoplayer:exoplayer-core:2.18.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\51b16512a656ca1125c5acd36144cb6d\transformed\exoplayer-core-2.18.1\jars\classes.jar"
      resolved="com.google.android.exoplayer:exoplayer-core:2.18.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\51b16512a656ca1125c5acd36144cb6d\transformed\exoplayer-core-2.18.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.exoplayer:exoplayer-common:2.18.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c392a2fb2c49f2619da5d035dfa5383a\transformed\exoplayer-common-2.18.1\jars\classes.jar"
      resolved="com.google.android.exoplayer:exoplayer-common:2.18.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c392a2fb2c49f2619da5d035dfa5383a\transformed\exoplayer-common-2.18.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.guava:guava:31.1-android@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.guava\guava\31.1-android\9222c47cc3ae890f07f7c961bbb3cb69050fe4aa\guava-31.1-android.jar"
      resolved="com.google.guava:guava:31.1-android"/>
  <library
      name="com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.guava\listenablefuture\9999.0-empty-to-avoid-conflict-with-guava\b421526c5f297295adef1c886e5246c39d4ac629\listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar"
      resolved="com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava"/>
  <library
      name="androidx.startup:startup-runtime:1.1.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\14af827455bcfcd04f2177e27d9bd4ea\transformed\startup-runtime-1.1.1\jars\classes.jar"
      resolved="androidx.startup:startup-runtime:1.1.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\14af827455bcfcd04f2177e27d9bd4ea\transformed\startup-runtime-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.tracing:tracing:1.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9385d3cdc0c788a5547cbf9c7da7c04e\transformed\tracing-1.2.0\jars\classes.jar"
      resolved="androidx.tracing:tracing:1.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9385d3cdc0c788a5547cbf9c7da7c04e\transformed\tracing-1.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.autofill:autofill:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\107f5fca7aa287004dce723186e118a0\transformed\autofill-1.1.0\jars\classes.jar"
      resolved="androidx.autofill:autofill:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\107f5fca7aa287004dce723186e118a0\transformed\autofill-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fbjni:fbjni:0.6.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6c49d09eb192753345178d717e7f7cc5\transformed\fbjni-0.6.0\jars\classes.jar"
      resolved="com.facebook.fbjni:fbjni:0.6.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6c49d09eb192753345178d717e7f7cc5\transformed\fbjni-0.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.soloader:soloader:0.12.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c023398c215bc75f2181552fb9fdab17\transformed\soloader-0.12.1\jars\classes.jar"
      resolved="com.facebook.soloader:soloader:0.12.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c023398c215bc75f2181552fb9fdab17\transformed\soloader-0.12.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.soloader:nativeloader:0.12.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.facebook.soloader\nativeloader\0.12.1\492cc5082540e19b29328f2f56c53255cb6e7cc6\nativeloader-0.12.1.jar"
      resolved="com.facebook.soloader:nativeloader:0.12.1"/>
  <library
      name="com.facebook.fresco:fresco:3.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\00d6599b202c8e6fa2efba643f940eaf\transformed\fresco-3.2.0\jars\classes.jar"
      resolved="com.facebook.fresco:fresco:3.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\00d6599b202c8e6fa2efba643f940eaf\transformed\fresco-3.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:fbcore:3.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6af87c0cef83f71025cc0c92d68393ab\transformed\fbcore-3.2.0\jars\classes.jar"
      resolved="com.facebook.fresco:fbcore:3.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6af87c0cef83f71025cc0c92d68393ab\transformed\fbcore-3.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.soloader:annotation:0.12.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.facebook.soloader\annotation\0.12.1\945ada76f62253ba8e72cbf755d0e85ea7362cfe\annotation-0.12.1.jar"
      resolved="com.facebook.soloader:annotation:0.12.1"/>
  <library
      name="com.facebook.fresco:ui-common:3.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\008ba41c11c05c6b486655c87cfbb57c\transformed\ui-common-3.2.0\jars\classes.jar"
      resolved="com.facebook.fresco:ui-common:3.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\008ba41c11c05c6b486655c87cfbb57c\transformed\ui-common-3.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:middleware:3.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6a59d052afabf217e5b9cd40acd209f4\transformed\middleware-3.2.0\jars\classes.jar"
      resolved="com.facebook.fresco:middleware:3.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6a59d052afabf217e5b9cd40acd209f4\transformed\middleware-3.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:drawee:3.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fab57e9ad1466a0f4700dc666713f174\transformed\drawee-3.2.0\jars\classes.jar"
      resolved="com.facebook.fresco:drawee:3.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fab57e9ad1466a0f4700dc666713f174\transformed\drawee-3.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:imagepipeline:3.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\95ea096b025b70bd6dac4a0d6de7f669\transformed\imagepipeline-3.2.0\jars\classes.jar"
      resolved="com.facebook.fresco:imagepipeline:3.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\95ea096b025b70bd6dac4a0d6de7f669\transformed\imagepipeline-3.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:imagepipeline-base:3.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0f42d0c7f198f628978efe9f1db5c8cc\transformed\imagepipeline-base-3.2.0\jars\classes.jar"
      resolved="com.facebook.fresco:imagepipeline-base:3.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0f42d0c7f198f628978efe9f1db5c8cc\transformed\imagepipeline-base-3.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.infer.annotation:infer-annotation:0.18.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.facebook.infer.annotation\infer-annotation\0.18.0\27539793fe93ed7d92b6376281c16cda8278ab2f\infer-annotation-0.18.0.jar"
      resolved="com.facebook.infer.annotation:infer-annotation:0.18.0"/>
  <library
      name="com.google.code.findbugs:jsr305:3.0.2@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.code.findbugs\jsr305\3.0.2\25ea2e8b0c338a877313bd4672d3fe056ea78f0d\jsr305-3.0.2.jar"
      resolved="com.google.code.findbugs:jsr305:3.0.2"/>
  <library
      name="org.jetbrains.kotlin:kotlin-annotations-jvm:1.3.72@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-annotations-jvm\1.3.72\7dba6c57de526588d8080317bda0c14cd88c8055\kotlin-annotations-jvm-1.3.72.jar"
      resolved="org.jetbrains.kotlin:kotlin-annotations-jvm:1.3.72"/>
  <library
      name="com.facebook.fresco:imagepipeline-native:3.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1751f1ad5da269f1d2af0a23df38336b\transformed\imagepipeline-native-3.2.0\jars\classes.jar"
      resolved="com.facebook.fresco:imagepipeline-native:3.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1751f1ad5da269f1d2af0a23df38336b\transformed\imagepipeline-native-3.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:memory-type-ashmem:3.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ccdd855833f2bd93117fdcdc2d273cbd\transformed\memory-type-ashmem-3.2.0\jars\classes.jar"
      resolved="com.facebook.fresco:memory-type-ashmem:3.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ccdd855833f2bd93117fdcdc2d273cbd\transformed\memory-type-ashmem-3.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:memory-type-native:3.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0918504feb2a3925f003cedcb7c50263\transformed\memory-type-native-3.2.0\jars\classes.jar"
      resolved="com.facebook.fresco:memory-type-native:3.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0918504feb2a3925f003cedcb7c50263\transformed\memory-type-native-3.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:memory-type-java:3.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9012310371af19960dba86c2063104b1\transformed\memory-type-java-3.2.0\jars\classes.jar"
      resolved="com.facebook.fresco:memory-type-java:3.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9012310371af19960dba86c2063104b1\transformed\memory-type-java-3.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:nativeimagefilters:3.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c3de5f5f3ca80f0a0ad8f67e1279747a\transformed\nativeimagefilters-3.2.0\jars\classes.jar"
      resolved="com.facebook.fresco:nativeimagefilters:3.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c3de5f5f3ca80f0a0ad8f67e1279747a\transformed\nativeimagefilters-3.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:nativeimagetranscoder:3.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e04fb5a673230bee0815eae9d213513b\transformed\nativeimagetranscoder-3.2.0\jars\classes.jar"
      resolved="com.facebook.fresco:nativeimagetranscoder:3.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e04fb5a673230bee0815eae9d213513b\transformed\nativeimagetranscoder-3.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.yoga:proguard-annotations:1.19.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.facebook.yoga\proguard-annotations\1.19.0\fcbbb39052e6490eaaf6a6959c49c3a4fbe87c63\proguard-annotations-1.19.0.jar"
      resolved="com.facebook.yoga:proguard-annotations:1.19.0"/>
  <library
      name="javax.inject:javax.inject:1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\javax.inject\javax.inject\1\6975da39a7040257bd51d21a231b76c915872d38\javax.inject-1.jar"
      resolved="javax.inject:javax.inject:1"/>
  <library
      name="com.google.guava:failureaccess:1.0.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.guava\failureaccess\1.0.1\1dcf1de382a0bf95a3d8b0849546c88bac1292c9\failureaccess-1.0.1.jar"
      resolved="com.google.guava:failureaccess:1.0.1"/>
  <library
      name="com.google.android.exoplayer:exoplayer-database:2.18.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c3e93635599c4b1e16eaa727c98651ba\transformed\exoplayer-database-2.18.1\jars\classes.jar"
      resolved="com.google.android.exoplayer:exoplayer-database:2.18.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c3e93635599c4b1e16eaa727c98651ba\transformed\exoplayer-database-2.18.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.exoplayer:exoplayer-datasource:2.18.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4142a06aa8b28b44645c452dc8e7e6e8\transformed\exoplayer-datasource-2.18.1\jars\classes.jar"
      resolved="com.google.android.exoplayer:exoplayer-datasource:2.18.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4142a06aa8b28b44645c452dc8e7e6e8\transformed\exoplayer-datasource-2.18.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.exoplayer:exoplayer-decoder:2.18.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a0f0d6a16104fd14eb1dbe4023f7e715\transformed\exoplayer-decoder-2.18.1\jars\classes.jar"
      resolved="com.google.android.exoplayer:exoplayer-decoder:2.18.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a0f0d6a16104fd14eb1dbe4023f7e715\transformed\exoplayer-decoder-2.18.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.exoplayer:exoplayer-extractor:2.18.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\59f2e6653accfd1fa031299e83183706\transformed\exoplayer-extractor-2.18.1\jars\classes.jar"
      resolved="com.google.android.exoplayer:exoplayer-extractor:2.18.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\59f2e6653accfd1fa031299e83183706\transformed\exoplayer-extractor-2.18.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.exoplayer:exoplayer-dash:2.18.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1e5980be30d8b059fea44a2e540c466f\transformed\exoplayer-dash-2.18.1\jars\classes.jar"
      resolved="com.google.android.exoplayer:exoplayer-dash:2.18.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1e5980be30d8b059fea44a2e540c466f\transformed\exoplayer-dash-2.18.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.exoplayer:exoplayer-hls:2.18.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cc616d8b7e0b362cc77fa1f56dcf1dab\transformed\exoplayer-hls-2.18.1\jars\classes.jar"
      resolved="com.google.android.exoplayer:exoplayer-hls:2.18.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cc616d8b7e0b362cc77fa1f56dcf1dab\transformed\exoplayer-hls-2.18.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.exoplayer:exoplayer-rtsp:2.18.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5943f916b9c87fd9465af3cff87eb724\transformed\exoplayer-rtsp-2.18.1\jars\classes.jar"
      resolved="com.google.android.exoplayer:exoplayer-rtsp:2.18.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5943f916b9c87fd9465af3cff87eb724\transformed\exoplayer-rtsp-2.18.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.exoplayer:exoplayer-smoothstreaming:2.18.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\344d65ac90f62d25a45d26a867e32306\transformed\exoplayer-smoothstreaming-2.18.1\jars\classes.jar"
      resolved="com.google.android.exoplayer:exoplayer-smoothstreaming:2.18.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\344d65ac90f62d25a45d26a867e32306\transformed\exoplayer-smoothstreaming-2.18.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="commons-io:commons-io:2.6@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\commons-io\commons-io\2.6\815893df5f31da2ece4040fe0a12fd44b577afaf\commons-io-2.6.jar"
      resolved="commons-io:commons-io:2.6"/>
  <library
      name="com.facebook.device.yearclass:yearclass:2.1.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.facebook.device.yearclass\yearclass\2.1.0\ef7d013a0140137b4a948dd65b46a08205d21020\yearclass-2.1.0.jar"
      resolved="com.facebook.device.yearclass:yearclass:2.1.0"/>
  <library
      name="commons-codec:commons-codec:1.10@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\commons-codec\commons-codec\1.10\4b95f4897fa13f2cd904aee711aeafc0c5295cd8\commons-codec-1.10.jar"
      resolved="commons-codec:commons-codec:1.10"/>
  <library
      name="com.github.bumptech.glide:disklrucache:4.16.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.github.bumptech.glide\disklrucache\4.16.0\411aa175d50d10b37c7a1a04d21a4e7145249557\disklrucache-4.16.0.jar"
      resolved="com.github.bumptech.glide:disklrucache:4.16.0"/>
  <library
      name="com.github.bumptech.glide:annotations:4.16.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.github.bumptech.glide\annotations\4.16.0\90730f6498299d207aa0878124ab7585969808f0\annotations-4.16.0.jar"
      resolved="com.github.bumptech.glide:annotations:4.16.0"/>
  <library
      name="androidx.exifinterface:exifinterface:1.3.6@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\58bc2a53808f1e7ef7f0a9798a6e8d3b\transformed\exifinterface-1.3.6\jars\classes.jar"
      resolved="androidx.exifinterface:exifinterface:1.3.6"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\58bc2a53808f1e7ef7f0a9798a6e8d3b\transformed\exifinterface-1.3.6"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:expo-application::release"
      project=":expo-application"/>
  <library
      name=":@@:expo-asset::release"
      project=":expo-asset"/>
  <library
      name=":@@:expo-clipboard::release"
      project=":expo-clipboard"/>
  <library
      name=":@@:expo-font::release"
      project=":expo-font"/>
  <library
      name=":@@:expo-image-picker::release"
      project=":expo-image-picker"/>
  <library
      name=":@@:expo-json-utils::release"
      project=":expo-json-utils"/>
  <library
      name=":@@:expo-keep-awake::release"
      project=":expo-keep-awake"/>
  <library
      name=":@@:expo-linear-gradient::release"
      project=":expo-linear-gradient"/>
  <library
      name=":@@:expo-manifests::release"
      project=":expo-manifests"/>
  <library
      name=":@@:expo-notifications::release"
      project=":expo-notifications"/>
  <library
      name=":@@:expo-speech::release"
      project=":expo-speech"/>
  <library
      name=":@@:expo-splash-screen::release"
      project=":expo-splash-screen"/>
  <library
      name=":@@:expo-system-ui::release"
      project=":expo-system-ui"/>
  <library
      name=":@@:expo-dev-menu-interface::release"
      project=":expo-dev-menu-interface"/>
  <library
      name=":@@:expo-updates-interface::release"
      project=":expo-updates-interface"/>
  <library
      name="com.github.zacharee:AndroidPdfViewer:4.0.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bfbeacde1e2a136fc2f5d733a2272ae4\transformed\AndroidPdfViewer-4.0.1\jars\classes.jar"
      resolved="com.github.zacharee:AndroidPdfViewer:4.0.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bfbeacde1e2a136fc2f5d733a2272ae4\transformed\AndroidPdfViewer-4.0.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="io.legere:pdfiumandroid:1.0.24@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\619ec02429e20903f7c51e07120c4bc9\transformed\pdfiumandroid-1.0.24\jars\classes.jar"
      resolved="io.legere:pdfiumandroid:1.0.24"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\619ec02429e20903f7c51e07120c4bc9\transformed\pdfiumandroid-1.0.24"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-ads:22.6.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3767e343142fab2c839a998d5bfdc1ca\transformed\play-services-ads-22.6.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-ads:22.6.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3767e343142fab2c839a998d5bfdc1ca\transformed\play-services-ads-22.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.material:material:1.6.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d139f7b0159c383eb11b4ff91a7586c3\transformed\material-1.6.1\jars\classes.jar"
      resolved="com.google.android.material:material:1.6.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d139f7b0159c383eb11b4ff91a7586c3\transformed\material-1.6.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.webkit:webkit:1.4.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\133aabf8e754322f2cb4fe43452d5a9f\transformed\webkit-1.4.0\jars\classes.jar"
      resolved="androidx.webkit:webkit:1.4.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\133aabf8e754322f2cb4fe43452d5a9f\transformed\webkit-1.4.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.foundation:foundation-android:1.7.6@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c53b383fa8484255d5fd97643f23aa97\transformed\foundation-release\jars\classes.jar"
      resolved="androidx.compose.foundation:foundation-android:1.7.6"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c53b383fa8484255d5fd97643f23aa97\transformed\foundation-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.foundation:foundation-layout-android:1.7.6@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c60f3bec9c0b03f8452870e035bbd08c\transformed\foundation-layout-release\jars\classes.jar"
      resolved="androidx.compose.foundation:foundation-layout-android:1.7.6"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c60f3bec9c0b03f8452870e035bbd08c\transformed\foundation-layout-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.animation:animation-core-android:1.7.6@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ad81b6d1cb84f5684e9b81682e0ca580\transformed\animation-core-release\jars\classes.jar"
      resolved="androidx.compose.animation:animation-core-android:1.7.6"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ad81b6d1cb84f5684e9b81682e0ca580\transformed\animation-core-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.animation:animation-android:1.7.6@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ad23691f7d3f19fc91ace9bad9a5798b\transformed\animation-release\jars\classes.jar"
      resolved="androidx.compose.animation:animation-android:1.7.6"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ad23691f7d3f19fc91ace9bad9a5798b\transformed\animation-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-unit-android:1.7.6@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\77e7bab780abbe664a08b5b103a02f68\transformed\ui-unit-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-unit-android:1.7.6"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\77e7bab780abbe664a08b5b103a02f68\transformed\ui-unit-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-graphics-android:1.7.6@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a5c989dd9face8759e6b4208907fb707\transformed\ui-graphics-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-graphics-android:1.7.6"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a5c989dd9face8759e6b4208907fb707\transformed\ui-graphics-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-geometry-android:1.7.6@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d7ad71823a61a5f0a7f36b3db6cab4f0\transformed\ui-geometry-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-geometry-android:1.7.6"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d7ad71823a61a5f0a7f36b3db6cab4f0\transformed\ui-geometry-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-util-android:1.7.6@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\269c404d6a010d64ee008714e750213e\transformed\ui-util-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-util-android:1.7.6"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\269c404d6a010d64ee008714e750213e\transformed\ui-util-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-text-android:1.7.6@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\93bc54070be88db5eae779acdf9e12b7\transformed\ui-text-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-text-android:1.7.6"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\93bc54070be88db5eae779acdf9e12b7\transformed\ui-text-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-android:1.7.6@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\491ff7541ddc657f2c55a259a03f9653\transformed\ui-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-android:1.7.6"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\491ff7541ddc657f2c55a259a03f9653\transformed\ui-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:animated-base:3.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fd354145b4068de20cd6c545093718f7\transformed\animated-base-3.2.0\jars\classes.jar"
      resolved="com.facebook.fresco:animated-base:3.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fd354145b4068de20cd6c545093718f7\transformed\animated-base-3.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:animated-drawable:3.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2829c2c06fa13d98e7c86056b72e0d01\transformed\animated-drawable-3.2.0\jars\classes.jar"
      resolved="com.facebook.fresco:animated-drawable:3.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2829c2c06fa13d98e7c86056b72e0d01\transformed\animated-drawable-3.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:vito-options:3.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c73ac94f95faf808105f3bc6acadb84f\transformed\vito-options-3.2.0\jars\classes.jar"
      resolved="com.facebook.fresco:vito-options:3.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c73ac94f95faf808105f3bc6acadb84f\transformed\vito-options-3.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:soloader:3.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fd2e876d85ff3c89b8674216d2355906\transformed\soloader-3.2.0\jars\classes.jar"
      resolved="com.facebook.fresco:soloader:3.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fd2e876d85ff3c89b8674216d2355906\transformed\soloader-3.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.github.bumptech.glide:okhttp3-integration:4.12.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\89b40201870c87f9d6b8ecc165e999d7\transformed\okhttp3-integration-4.12.0\jars\classes.jar"
      resolved="com.github.bumptech.glide:okhttp3-integration:4.12.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\89b40201870c87f9d6b8ecc165e999d7\transformed\okhttp3-integration-4.12.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.github.CanHub:Android-Image-Cropper:4.3.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\084b6a12791f1cb8a95d16811ba661d8\transformed\Android-Image-Cropper-4.3.1\jars\classes.jar"
      resolved="com.github.CanHub:Android-Image-Cropper:4.3.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\084b6a12791f1cb8a95d16811ba661d8\transformed\Android-Image-Cropper-4.3.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.constraintlayout:constraintlayout:2.0.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8279ed973d5dcd215ba6b5d25375681a\transformed\constraintlayout-2.0.1\jars\classes.jar"
      resolved="androidx.constraintlayout:constraintlayout:2.0.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8279ed973d5dcd215ba6b5d25375681a\transformed\constraintlayout-2.0.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.core:core-splashscreen:1.2.0-alpha02@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b0a4e6e1d6ee657dfe4640d06de3f260\transformed\core-splashscreen-1.2.0-alpha02\jars\classes.jar"
      resolved="androidx.core:core-splashscreen:1.2.0-alpha02"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b0a4e6e1d6ee657dfe4640d06de3f260\transformed\core-splashscreen-1.2.0-alpha02"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-ads-lite:22.6.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6541660dec70774a79856ee0aa1798c7\transformed\play-services-ads-lite-22.6.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-ads-lite:22.6.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6541660dec70774a79856ee0aa1798c7\transformed\play-services-ads-lite-22.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-messaging:24.0.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c48c306fa29cacf340e1042583924ca8\transformed\firebase-messaging-24.0.1\jars\classes.jar"
      resolved="com.google.firebase:firebase-messaging:24.0.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c48c306fa29cacf340e1042583924ca8\transformed\firebase-messaging-24.0.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-ads-base:22.6.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7745c1592936a86096aad9cc2518d12c\transformed\play-services-ads-base-22.6.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-ads-base:22.6.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7745c1592936a86096aad9cc2518d12c\transformed\play-services-ads-base-22.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-ads-identifier:18.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d3bb42d4ec47f3a4b97c081e8fc745eb\transformed\play-services-ads-identifier-18.0.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-ads-identifier:18.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d3bb42d4ec47f3a4b97c081e8fc745eb\transformed\play-services-ads-identifier-18.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-appset:16.0.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1c096d6792c8b8a51e553fca6729fd91\transformed\play-services-appset-16.0.1\jars\classes.jar"
      resolved="com.google.android.gms:play-services-appset:16.0.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1c096d6792c8b8a51e553fca6729fd91\transformed\play-services-appset-16.0.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-installations:17.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3abe18b93b03aa88f4479351be1225c9\transformed\firebase-installations-17.2.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-installations:17.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3abe18b93b03aa88f4479351be1225c9\transformed\firebase-installations-17.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-common-ktx:21.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c58bf33df59e38220a67da64c5f2651f\transformed\firebase-common-ktx-21.0.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-common-ktx:21.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c58bf33df59e38220a67da64c5f2651f\transformed\firebase-common-ktx-21.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-common:21.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3af9264cb24c523080e789fe1aeda055\transformed\firebase-common-21.0.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-common:21.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3af9264cb24c523080e789fe1aeda055\transformed\firebase-common-21.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-iid-interop:17.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\236a5b6f818909e102be500f63dd114c\transformed\firebase-iid-interop-17.1.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-iid-interop:17.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\236a5b6f818909e102be500f63dd114c\transformed\firebase-iid-interop-17.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-measurement-connector:19.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\75f5899f7914c7e1ca3da488be347684\transformed\firebase-measurement-connector-19.0.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-measurement-connector:19.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\75f5899f7914c7e1ca3da488be347684\transformed\firebase-measurement-connector-19.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-base:18.0.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\263c8e8847d6991526302086ecc6e182\transformed\play-services-base-18.0.1\jars\classes.jar"
      resolved="com.google.android.gms:play-services-base:18.0.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\263c8e8847d6991526302086ecc6e182\transformed\play-services-base-18.0.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-cloud-messaging:17.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0ed922cc04554723c7a0e43e2f3e8a03\transformed\play-services-cloud-messaging-17.2.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-cloud-messaging:17.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0ed922cc04554723c7a0e43e2f3e8a03\transformed\play-services-cloud-messaging-17.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-stats:17.0.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\13585b41a7e3d716c24ae7640fd22990\transformed\play-services-stats-17.0.2\jars\classes.jar"
      resolved="com.google.android.gms:play-services-stats:17.0.2"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\13585b41a7e3d716c24ae7640fd22990\transformed\play-services-stats-17.0.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-measurement-sdk-api:20.1.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bc61480a01f4e8170b70ec692b89a4ef\transformed\play-services-measurement-sdk-api-20.1.2\jars\classes.jar"
      resolved="com.google.android.gms:play-services-measurement-sdk-api:20.1.2"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bc61480a01f4e8170b70ec692b89a4ef\transformed\play-services-measurement-sdk-api-20.1.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-installations-interop:17.1.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bdf7393e380b230bff26f1802a1b4f7d\transformed\firebase-installations-interop-17.1.1\jars\classes.jar"
      resolved="com.google.firebase:firebase-installations-interop:17.1.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bdf7393e380b230bff26f1802a1b4f7d\transformed\firebase-installations-interop-17.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.work:work-runtime:2.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\jars\classes.jar"
      resolved="androidx.work:work-runtime:2.7.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.dynamicanimation:dynamicanimation:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5e2f03cf9bd05bb6c8fdb926a0e12337\transformed\dynamicanimation-1.0.0\jars\classes.jar"
      resolved="androidx.dynamicanimation:dynamicanimation:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5e2f03cf9bd05bb6c8fdb926a0e12337\transformed\dynamicanimation-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.emoji2:emoji2-views-helper:1.3.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1a9708e60093619fa9b857d97b0d2466\transformed\emoji2-views-helper-1.3.0\jars\classes.jar"
      resolved="androidx.emoji2:emoji2-views-helper:1.3.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1a9708e60093619fa9b857d97b0d2466\transformed\emoji2-views-helper-1.3.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.emoji2:emoji2:1.3.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\270359720fa256563d55dec8f9480bc0\transformed\emoji2-1.3.0\jars\classes.jar;C:\Users\<USER>\.gradle\caches\8.10.2\transforms\270359720fa256563d55dec8f9480bc0\transformed\emoji2-1.3.0\jars\libs\repackaged.jar"
      resolved="androidx.emoji2:emoji2:1.3.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\270359720fa256563d55dec8f9480bc0\transformed\emoji2-1.3.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.activity:activity-ktx:1.7.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a359aa7ac01bcb384a2fbbb5b13a6694\transformed\activity-ktx-1.7.2\jars\classes.jar"
      resolved="androidx.activity:activity-ktx:1.7.2"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a359aa7ac01bcb384a2fbbb5b13a6694\transformed\activity-ktx-1.7.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.savedstate:savedstate-ktx:1.2.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8607a01d59feef757df73ca21332412c\transformed\savedstate-ktx-1.2.1\jars\classes.jar"
      resolved="androidx.savedstate:savedstate-ktx:1.2.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8607a01d59feef757df73ca21332412c\transformed\savedstate-ktx-1.2.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.3@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9045d761b71bd1c93bb339f42850cea1\transformed\lifecycle-runtime-ktx-release\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.3"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9045d761b71bd1c93bb339f42850cea1\transformed\lifecycle-runtime-ktx-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.3@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22e25f5ac57dc71f2927be11aaad489a\transformed\lifecycle-viewmodel-ktx-2.8.3\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.3"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22e25f5ac57dc71f2927be11aaad489a\transformed\lifecycle-viewmodel-ktx-2.8.3"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-common-java8:2.8.3@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.lifecycle\lifecycle-common-java8\2.8.3\2ad14aed781c4a73ed4dbb421966d408a0a06686\lifecycle-common-java8-2.8.3.jar"
      resolved="androidx.lifecycle:lifecycle-common-java8:2.8.3"/>
  <library
      name="androidx.lifecycle:lifecycle-runtime-compose-android:2.8.3@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\921f79f957b1ab4204310daa7d00252c\transformed\lifecycle-runtime-compose-release\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-runtime-compose-android:2.8.3"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\921f79f957b1ab4204310daa7d00252c\transformed\lifecycle-runtime-compose-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.runtime:runtime-saveable-android:1.7.6@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\725ea4bd61c0ccdf4cf28bda20de4690\transformed\runtime-saveable-release\jars\classes.jar"
      resolved="androidx.compose.runtime:runtime-saveable-android:1.7.6"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\725ea4bd61c0ccdf4cf28bda20de4690\transformed\runtime-saveable-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.runtime:runtime-android:1.7.6@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d489a1acd0bfea4a719ae956ede04649\transformed\runtime-release\jars\classes.jar"
      resolved="androidx.compose.runtime:runtime-android:1.7.6"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d489a1acd0bfea4a719ae956ede04649\transformed\runtime-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e467191d6f3701eee161366d50b43d22\transformed\ads-adservices-java-1.0.0-beta05\jars\classes.jar"
      resolved="androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e467191d6f3701eee161366d50b43d22\transformed\ads-adservices-java-1.0.0-beta05"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\701c966945a05b6cfc9f2e519b7eeb8e\transformed\ads-adservices-1.0.0-beta05\jars\classes.jar"
      resolved="androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\701c966945a05b6cfc9f2e519b7eeb8e\transformed\ads-adservices-1.0.0-beta05"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-coroutines-play-services:1.8.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlinx\kotlinx-coroutines-play-services\1.8.1\71eb0cace3aaa93591a613a32c92853c464d2a53\kotlinx-coroutines-play-services-1.8.1.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-coroutines-play-services:1.8.1"/>
  <library
      name="com.google.android.gms:play-services-tasks:18.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d76113af3f7d3d13e730bfbd6c5ce868\transformed\play-services-tasks-18.1.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-tasks:18.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d76113af3f7d3d13e730bfbd6c5ce868\transformed\play-services-tasks-18.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-measurement-base:20.1.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a53657193bce59b9049b26d13a09b011\transformed\play-services-measurement-base-20.1.2\jars\classes.jar"
      resolved="com.google.android.gms:play-services-measurement-base:20.1.2"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a53657193bce59b9049b26d13a09b011\transformed\play-services-measurement-base-20.1.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.viewpager2:viewpager2:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7153d99efb7d504a600b80818f6dd410\transformed\viewpager2-1.0.0\jars\classes.jar"
      resolved="androidx.viewpager2:viewpager2:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7153d99efb7d504a600b80818f6dd410\transformed\viewpager2-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.fragment:fragment-ktx:1.6.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c828bcd5d8d4af3c3cb700d9a94ca575\transformed\fragment-ktx-1.6.1\jars\classes.jar"
      resolved="androidx.fragment:fragment-ktx:1.6.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c828bcd5d8d4af3c3cb700d9a94ca575\transformed\fragment-ktx-1.6.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.customview:customview-poolingcontainer:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\784c3e1e4045c99081885364c317d10a\transformed\customview-poolingcontainer-1.0.0\jars\classes.jar"
      resolved="androidx.customview:customview-poolingcontainer:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\784c3e1e4045c99081885364c317d10a\transformed\customview-poolingcontainer-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.transition:transition:1.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ec5e240ff5e6e23b7d3760d498697fde\transformed\transition-1.2.0\jars\classes.jar"
      resolved="androidx.transition:transition:1.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ec5e240ff5e6e23b7d3760d498697fde\transformed\transition-1.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.recyclerview:recyclerview:1.2.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c89ec4c3b0bf0366128af44077f2246a\transformed\recyclerview-1.2.1\jars\classes.jar"
      resolved="androidx.recyclerview:recyclerview:1.2.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c89ec4c3b0bf0366128af44077f2246a\transformed\recyclerview-1.2.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.graphics:graphics-path:1.0.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\249bdb5e46167092cbcb5c28c6def0b4\transformed\graphics-path-1.0.1\jars\classes.jar"
      resolved="androidx.graphics:graphics-path:1.0.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\249bdb5e46167092cbcb5c28c6def0b4\transformed\graphics-path-1.0.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="org.jetbrains.kotlin:kotlin-reflect:1.9.25@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-reflect\1.9.25\73023c38b7b20430232893cf9b556dc8486e07a4\kotlin-reflect-1.9.25.jar"
      resolved="org.jetbrains.kotlin:kotlin-reflect:1.9.25"/>
  <library
      name="androidx.profileinstaller:profileinstaller:1.3.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b4fc0c5c3b80165703f803a1e31a276f\transformed\profileinstaller-1.3.1\jars\classes.jar"
      resolved="androidx.profileinstaller:profileinstaller:1.3.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b4fc0c5c3b80165703f803a1e31a276f\transformed\profileinstaller-1.3.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.tracing:tracing-ktx:1.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7f411934f74ee5ee3cb40c605564e074\transformed\tracing-ktx-1.2.0\jars\classes.jar"
      resolved="androidx.tracing:tracing-ktx:1.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7f411934f74ee5ee3cb40c605564e074\transformed\tracing-ktx-1.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="org.jetbrains.kotlin:kotlin-parcelize-runtime:1.9.25@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-parcelize-runtime\1.9.25\5ddaf36e1f9708ffd4019de9757ba813bd0a1421\kotlin-parcelize-runtime-1.9.25.jar"
      resolved="org.jetbrains.kotlin:kotlin-parcelize-runtime:1.9.25"/>
  <library
      name="androidx.cardview:cardview:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4bd4c83d83163ee2b2c6f36ddb26164c\transformed\cardview-1.0.0\jars\classes.jar"
      resolved="androidx.cardview:cardview:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4bd4c83d83163ee2b2c6f36ddb26164c\transformed\cardview-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-components:18.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f6133db78b2466f306357ecca4f7670\transformed\firebase-components-18.0.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-components:18.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f6133db78b2466f306357ecca4f7670\transformed\firebase-components-18.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-datatransport:18.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c425ae13f88cc4eff0fa0c7b762ff882\transformed\firebase-datatransport-18.2.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-datatransport:18.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c425ae13f88cc4eff0fa0c7b762ff882\transformed\firebase-datatransport-18.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.datatransport:transport-backend-cct:3.1.9@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\37f7f33bfd0c5f48616a4338c6ce336d\transformed\transport-backend-cct-3.1.9\jars\classes.jar"
      resolved="com.google.android.datatransport:transport-backend-cct:3.1.9"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\37f7f33bfd0c5f48616a4338c6ce336d\transformed\transport-backend-cct-3.1.9"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-encoders-json:18.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\008e2aa488fe3f980517b851bceca6d9\transformed\firebase-encoders-json-18.0.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-encoders-json:18.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\008e2aa488fe3f980517b851bceca6d9\transformed\firebase-encoders-json-18.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.datatransport:transport-runtime:3.1.9@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8141aeabd6dd325d2cf83449b27069b5\transformed\transport-runtime-3.1.9\jars\classes.jar"
      resolved="com.google.android.datatransport:transport-runtime:3.1.9"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8141aeabd6dd325d2cf83449b27069b5\transformed\transport-runtime-3.1.9"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-encoders-proto:16.0.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.firebase\firebase-encoders-proto\16.0.0\a42d5fd83b96ae7b73a8617d29c94703e18c9992\firebase-encoders-proto-16.0.0.jar"
      resolved="com.google.firebase:firebase-encoders-proto:16.0.0"/>
  <library
      name="com.google.firebase:firebase-encoders:17.0.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.firebase\firebase-encoders\17.0.0\26f52dc549c42575b155f8c720e84059ee600a85\firebase-encoders-17.0.0.jar"
      resolved="com.google.firebase:firebase-encoders:17.0.0"/>
  <library
      name="com.google.android.datatransport:transport-api:3.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8d517e21cf066dcb14df0c1e7f217306\transformed\transport-api-3.1.0\jars\classes.jar"
      resolved="com.google.android.datatransport:transport-api:3.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8d517e21cf066dcb14df0c1e7f217306\transformed\transport-api-3.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.resourceinspection:resourceinspection-annotation:1.0.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.resourceinspection\resourceinspection-annotation\1.0.1\8c21f8ff5d96d5d52c948707f7e4d6ca6773feef\resourceinspection-annotation-1.0.1.jar"
      resolved="androidx.resourceinspection:resourceinspection-annotation:1.0.1"/>
  <library
      name="androidx.concurrent:concurrent-futures:1.1.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.concurrent\concurrent-futures\1.1.0\50b7fb98350d5f42a4e49704b03278542293ba48\concurrent-futures-1.1.0.jar"
      resolved="androidx.concurrent:concurrent-futures:1.1.0"/>
  <library
      name="androidx.room:room-runtime:2.2.5@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\178194cdae2c08446775c57801d0086b\transformed\room-runtime-2.2.5\jars\classes.jar"
      resolved="androidx.room:room-runtime:2.2.5"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\178194cdae2c08446775c57801d0086b\transformed\room-runtime-2.2.5"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.sqlite:sqlite-framework:2.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a10f823b501a997fa56d4dfa137e89df\transformed\sqlite-framework-2.1.0\jars\classes.jar"
      resolved="androidx.sqlite:sqlite-framework:2.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a10f823b501a997fa56d4dfa137e89df\transformed\sqlite-framework-2.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.sqlite:sqlite:2.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\76dbd3726635b50e2da312535b6549ad\transformed\sqlite-2.1.0\jars\classes.jar"
      resolved="androidx.sqlite:sqlite:2.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\76dbd3726635b50e2da312535b6549ad\transformed\sqlite-2.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.room:room-common:2.2.5@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.room\room-common\2.2.5\f5e3b73a0c2ab5e276e26868e4ce3542baede207\room-common-2.2.5.jar"
      resolved="androidx.room:room-common:2.2.5"/>
  <library
      name="androidx.collection:collection-ktx:1.4.4@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.collection\collection-ktx\1.4.4\2ad14aed781c4a73ed4dbb421966d408a0a06686\collection-ktx-1.4.4.jar"
      resolved="androidx.collection:collection-ktx:1.4.4"/>
  <library
      name="org.jetbrains.kotlin:kotlin-android-extensions-runtime:1.9.25@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-android-extensions-runtime\1.9.25\3c69ebd730fc998c00b5d6d69f637cd12231028\kotlin-android-extensions-runtime-1.9.25.jar"
      resolved="org.jetbrains.kotlin:kotlin-android-extensions-runtime:1.9.25"/>
  <library
      name="com.google.code.gson:gson:2.8.6@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.code.gson\gson\2.8.6\9180733b7df8542621dc12e21e87557e8c99b8cb\gson-2.8.6.jar"
      resolved="com.google.code.gson:gson:2.8.6"/>
  <library
      name="com.google.firebase:firebase-annotations:16.2.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.firebase\firebase-annotations\16.2.0\ba0806703ca285d03fa9c888b5868f101134a501\firebase-annotations-16.2.0.jar"
      resolved="com.google.firebase:firebase-annotations:16.2.0"/>
  <library
      name="com.parse.bolts:bolts-tasks:1.4.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.parse.bolts\bolts-tasks\1.4.0\d85884acf6810a3bbbecb587f239005cbc846dc4\bolts-tasks-1.4.0.jar"
      resolved="com.parse.bolts:bolts-tasks:1.4.0"/>
  <library
      name="com.android.installreferrer:installreferrer:2.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\54c08d13b2ebc95f93ef63a72922c9d6\transformed\installreferrer-2.2\jars\classes.jar"
      resolved="com.android.installreferrer:installreferrer:2.2"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\54c08d13b2ebc95f93ef63a72922c9d6\transformed\installreferrer-2.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="me.leolin:ShortcutBadger:1.1.22@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f9b119240edcbcd33147b347e305b41e\transformed\ShortcutBadger-1.1.22\jars\classes.jar"
      resolved="me.leolin:ShortcutBadger:1.1.22"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f9b119240edcbcd33147b347e305b41e\transformed\ShortcutBadger-1.1.22"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.errorprone:error_prone_annotations:2.26.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.errorprone\error_prone_annotations\2.26.0\c513866fd91bb46587500440a80fa943e95d12d9\error_prone_annotations-2.26.0.jar"
      resolved="com.google.errorprone:error_prone_annotations:2.26.0"/>
  <library
      name="androidx.constraintlayout:constraintlayout-solver:2.0.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.constraintlayout\constraintlayout-solver\2.0.1\30988fe2d77f3fe3bf7551bb8a8b795fad7e7226\constraintlayout-solver-2.0.1.jar"
      resolved="androidx.constraintlayout:constraintlayout-solver:2.0.1"/>
  <library
      name="com.facebook.fresco:vito-renderer:3.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ffeb2fbb6ecc8549e1607dcf7031595c\transformed\vito-renderer-3.2.0\jars\classes.jar"
      resolved="com.facebook.fresco:vito-renderer:3.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ffeb2fbb6ecc8549e1607dcf7031595c\transformed\vito-renderer-3.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="org.checkerframework:checker-qual:3.12.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.checkerframework\checker-qual\3.12.0\d5692f0526415fcc6de94bb5bfbd3afd9dd3b3e5\checker-qual-3.12.0.jar"
      resolved="org.checkerframework:checker-qual:3.12.0"/>
  <library
      name="com.google.j2objc:j2objc-annotations:1.3@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.j2objc\j2objc-annotations\1.3\ba035118bc8bac37d7eff77700720999acd9986d\j2objc-annotations-1.3.jar"
      resolved="com.google.j2objc:j2objc-annotations:1.3"/>
</libraries>
