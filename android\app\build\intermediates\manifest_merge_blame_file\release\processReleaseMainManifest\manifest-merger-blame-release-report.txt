1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.gokul719.snack97152fc1f368437dac54171df4ba22bd"
4    android:versionCode="5"
5    android:versionName="1.3.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="34" />
10
11    <uses-permission android:name="android.permission.CAMERA" />
11-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:2:3-62
11-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:2:20-60
12    <uses-permission android:name="android.permission.INTERNET" />
12-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:3:3-64
12-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:3:20-62
13    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
13-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:4:3-77
13-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:4:20-75
14    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
14-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:5:3-77
14-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:5:20-75
15    <uses-permission android:name="android.permission.RECORD_AUDIO" />
15-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:6:3-68
15-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:6:20-66
16    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
16-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:7:3-75
16-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:7:20-73
17    <uses-permission android:name="android.permission.VIBRATE" />
17-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:8:3-63
17-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:8:20-61
18    <uses-permission android:name="android.permission.WAKE_LOCK" />
18-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:9:3-65
18-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:9:20-63
19    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
19-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:10:3-78
19-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:10:20-76
20    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
20-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:11:3-76
20-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:11:20-74
21
22    <queries>
22-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:12:3-18:13
23        <intent>
23-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:13:5-17:14
24            <action android:name="android.intent.action.VIEW" />
24-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:14:7-58
24-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:14:15-56
25
26            <category android:name="android.intent.category.BROWSABLE" />
26-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:15:7-67
26-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:15:17-65
27
28            <data android:scheme="https" />
28-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:16:7-37
28-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:16:13-35
29        </intent>
30        <!-- Query open documents -->
31        <intent>
31-->[:expo-file-system] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:15:9-17:18
32            <action android:name="android.intent.action.OPEN_DOCUMENT_TREE" />
32-->[:expo-file-system] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:16:13-79
32-->[:expo-file-system] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:16:21-76
33        </intent>
34        <intent>
34-->[:expo-image-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:15:9-19:18
35
36            <!-- Required for picking images from the camera roll if targeting API 30 -->
37            <action android:name="android.media.action.IMAGE_CAPTURE" />
37-->[:expo-image-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:18:13-73
37-->[:expo-image-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:18:21-70
38        </intent>
39        <intent>
39-->[:expo-image-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:20:9-24:18
40
41            <!-- Required for picking images from the camera if targeting API 30 -->
42            <action android:name="android.media.action.ACTION_VIDEO_CAPTURE" />
42-->[:expo-image-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:23:13-80
42-->[:expo-image-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:23:21-77
43        </intent>
44        <intent>
44-->[:expo-sharing] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-sharing\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:9-14:18
45
46            <!-- Required for file sharing if targeting API 30 -->
47            <action android:name="android.intent.action.SEND" />
47-->[:expo-sharing] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-sharing\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:13-65
47-->[:expo-sharing] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-sharing\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:21-62
48
49            <data android:mimeType="*/*" />
49-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:16:7-37
50        </intent>
51        <intent>
51-->[:expo-speech] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-speech\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:9-12:18
52
53            <!-- Required for text-to-speech if targeting API 30 -->
54            <action android:name="android.intent.action.TTS_SERVICE" />
54-->[:expo-speech] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-speech\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:13-72
54-->[:expo-speech] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-speech\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:21-69
55        </intent>
56        <intent>
56-->[com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\084b6a12791f1cb8a95d16811ba661d8\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:10:9-16:18
57            <action android:name="android.intent.action.GET_CONTENT" />
57-->[com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\084b6a12791f1cb8a95d16811ba661d8\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:11:13-72
57-->[com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\084b6a12791f1cb8a95d16811ba661d8\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:11:21-69
58
59            <category android:name="android.intent.category.OPENABLE" />
59-->[com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\084b6a12791f1cb8a95d16811ba661d8\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:13:13-73
59-->[com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\084b6a12791f1cb8a95d16811ba661d8\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:13:23-70
60
61            <data android:mimeType="*/*" />
61-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:16:7-37
62        </intent> <!-- End of browser content -->
63        <!-- For CustomTabsService -->
64        <intent>
64-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6541660dec70774a79856ee0aa1798c7\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:47:9-49:18
65            <action android:name="android.support.customtabs.action.CustomTabsService" />
65-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6541660dec70774a79856ee0aa1798c7\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:48:13-90
65-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6541660dec70774a79856ee0aa1798c7\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:48:21-87
66        </intent>
67    </queries>
68
69    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
69-->[:react-native-google-mobile-ads] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-google-mobile-ads\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:10:5-79
69-->[:react-native-google-mobile-ads] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-google-mobile-ads\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:10:22-76
70    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
70-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:7:5-81
70-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:7:22-78
71    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
71-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:5-77
71-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:22-74
72    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID" />
72-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6541660dec70774a79856ee0aa1798c7\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:27:5-82
72-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6541660dec70774a79856ee0aa1798c7\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:27:22-79
73    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION" />
73-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6541660dec70774a79856ee0aa1798c7\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:28:5-88
73-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6541660dec70774a79856ee0aa1798c7\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:28:22-85
74    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_TOPICS" /> <!-- Required by older versions of Google Play services to create IID tokens -->
74-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6541660dec70774a79856ee0aa1798c7\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:29:5-83
74-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6541660dec70774a79856ee0aa1798c7\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:29:22-80
75    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />
75-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c48c306fa29cacf340e1042583924ca8\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:26:5-82
75-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c48c306fa29cacf340e1042583924ca8\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:26:22-79
76    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
76-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:28:5-77
76-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:28:22-74
77
78    <permission
78-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71743d480ebb399fb4fe6b96265e7bca\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
79        android:name="com.gokul719.snack97152fc1f368437dac54171df4ba22bd.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
79-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71743d480ebb399fb4fe6b96265e7bca\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
80        android:protectionLevel="signature" />
80-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71743d480ebb399fb4fe6b96265e7bca\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
81
82    <uses-permission android:name="com.gokul719.snack97152fc1f368437dac54171df4ba22bd.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
82-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71743d480ebb399fb4fe6b96265e7bca\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
82-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71743d480ebb399fb4fe6b96265e7bca\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
83    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" /> <!-- for android -->
83-->[com.android.installreferrer:installreferrer:2.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\54c08d13b2ebc95f93ef63a72922c9d6\transformed\installreferrer-2.2\AndroidManifest.xml:9:5-110
83-->[com.android.installreferrer:installreferrer:2.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\54c08d13b2ebc95f93ef63a72922c9d6\transformed\installreferrer-2.2\AndroidManifest.xml:9:22-107
84    <!-- <uses-permission android:name="com.android.launcher.permission.READ_SETTINGS"/> -->
85    <!-- <uses-permission android:name="com.android.launcher.permission.WRITE_SETTINGS"/> -->
86    <!-- <uses-permission android:name="com.android.launcher.permission.INSTALL_SHORTCUT" /> -->
87    <!-- <uses-permission android:name="com.android.launcher.permission.UNINSTALL_SHORTCUT" /> -->
88    <!-- for Samsung -->
89    <uses-permission android:name="com.sec.android.provider.badge.permission.READ" />
89-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f9b119240edcbcd33147b347e305b41e\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:19:5-86
89-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f9b119240edcbcd33147b347e305b41e\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:19:22-83
90    <uses-permission android:name="com.sec.android.provider.badge.permission.WRITE" /> <!-- for htc -->
90-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f9b119240edcbcd33147b347e305b41e\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:20:5-87
90-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f9b119240edcbcd33147b347e305b41e\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:20:22-84
91    <uses-permission android:name="com.htc.launcher.permission.READ_SETTINGS" />
91-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f9b119240edcbcd33147b347e305b41e\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:23:5-81
91-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f9b119240edcbcd33147b347e305b41e\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:23:22-78
92    <uses-permission android:name="com.htc.launcher.permission.UPDATE_SHORTCUT" /> <!-- for sony -->
92-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f9b119240edcbcd33147b347e305b41e\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:24:5-83
92-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f9b119240edcbcd33147b347e305b41e\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:24:22-80
93    <uses-permission android:name="com.sonyericsson.home.permission.BROADCAST_BADGE" />
93-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f9b119240edcbcd33147b347e305b41e\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:27:5-88
93-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f9b119240edcbcd33147b347e305b41e\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:27:22-85
94    <uses-permission android:name="com.sonymobile.home.permission.PROVIDER_INSERT_BADGE" /> <!-- for apex -->
94-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f9b119240edcbcd33147b347e305b41e\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:28:5-92
94-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f9b119240edcbcd33147b347e305b41e\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:28:22-89
95    <uses-permission android:name="com.anddoes.launcher.permission.UPDATE_COUNT" /> <!-- for solid -->
95-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f9b119240edcbcd33147b347e305b41e\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:31:5-84
95-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f9b119240edcbcd33147b347e305b41e\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:31:22-81
96    <uses-permission android:name="com.majeur.launcher.permission.UPDATE_BADGE" /> <!-- for huawei -->
96-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f9b119240edcbcd33147b347e305b41e\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:34:5-83
96-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f9b119240edcbcd33147b347e305b41e\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:34:22-80
97    <uses-permission android:name="com.huawei.android.launcher.permission.CHANGE_BADGE" />
97-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f9b119240edcbcd33147b347e305b41e\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:37:5-91
97-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f9b119240edcbcd33147b347e305b41e\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:37:22-88
98    <uses-permission android:name="com.huawei.android.launcher.permission.READ_SETTINGS" />
98-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f9b119240edcbcd33147b347e305b41e\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:38:5-92
98-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f9b119240edcbcd33147b347e305b41e\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:38:22-89
99    <uses-permission android:name="com.huawei.android.launcher.permission.WRITE_SETTINGS" /> <!-- for ZUK -->
99-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f9b119240edcbcd33147b347e305b41e\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:39:5-93
99-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f9b119240edcbcd33147b347e305b41e\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:39:22-90
100    <uses-permission android:name="android.permission.READ_APP_BADGE" /> <!-- for OPPO -->
100-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f9b119240edcbcd33147b347e305b41e\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:42:5-73
100-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f9b119240edcbcd33147b347e305b41e\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:42:22-70
101    <uses-permission android:name="com.oppo.launcher.permission.READ_SETTINGS" />
101-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f9b119240edcbcd33147b347e305b41e\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:45:5-82
101-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f9b119240edcbcd33147b347e305b41e\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:45:22-79
102    <uses-permission android:name="com.oppo.launcher.permission.WRITE_SETTINGS" /> <!-- for EvMe -->
102-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f9b119240edcbcd33147b347e305b41e\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:46:5-83
102-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f9b119240edcbcd33147b347e305b41e\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:46:22-80
103    <uses-permission android:name="me.everything.badger.permission.BADGE_COUNT_READ" />
103-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f9b119240edcbcd33147b347e305b41e\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:49:5-88
103-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f9b119240edcbcd33147b347e305b41e\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:49:22-85
104    <uses-permission android:name="me.everything.badger.permission.BADGE_COUNT_WRITE" />
104-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f9b119240edcbcd33147b347e305b41e\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:50:5-89
104-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f9b119240edcbcd33147b347e305b41e\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:50:22-86
105
106    <application
106-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:19:3-43:17
107        android:name="com.gokul719.snack97152fc1f368437dac54171df4ba22bd.MainApplication"
107-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:19:16-47
108        android:allowBackup="true"
108-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:19:162-188
109        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
109-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:19:248-316
110        android:extractNativeLibs="false"
111        android:icon="@mipmap/ic_launcher"
111-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:19:81-115
112        android:label="@string/app_name"
112-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:19:48-80
113        android:roundIcon="@mipmap/ic_launcher_round"
113-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:19:116-161
114        android:supportsRtl="true"
114-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:19:221-247
115        android:theme="@style/AppTheme" >
115-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:19:189-220
116        <meta-data
116-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:20:5-159
117            android:name="com.google.android.gms.ads.APPLICATION_ID"
117-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:20:16-72
118            android:value="ca-app-pub-9706687137550019~9208363455" />
118-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:20:73-127
119        <meta-data
119-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:20:5-159
120            android:name="com.google.android.gms.ads.APPLICATION_ID"
120-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:20:16-72
121            android:value="ca-app-pub-9706687137550019~9208363455" />
121-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:20:73-127
122        <meta-data
122-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:22:5-137
123            android:name="com.google.android.gms.ads.DELAY_APP_MEASUREMENT_INIT"
123-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:22:16-84
124            android:value="true" />
124-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:22:85-105
125        <meta-data
125-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:23:5-135
126            android:name="com.google.android.gms.ads.flag.OPTIMIZE_AD_LOADING"
126-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:23:16-82
127            android:value="true" />
127-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:23:83-103
128        <meta-data
128-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:23:5-135
129            android:name="com.google.android.gms.ads.flag.OPTIMIZE_AD_LOADING"
129-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:23:16-82
130            android:value="true" />
130-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:23:83-103
131        <meta-data
131-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:25:5-139
132            android:name="com.google.android.gms.ads.flag.OPTIMIZE_INITIALIZATION"
132-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:25:16-86
133            android:value="true" />
133-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:25:87-107
134        <meta-data
134-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:25:5-139
135            android:name="com.google.android.gms.ads.flag.OPTIMIZE_INITIALIZATION"
135-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:25:16-86
136            android:value="true" />
136-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:25:87-107
137        <meta-data
137-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:27:5-83
138            android:name="expo.modules.updates.ENABLED"
138-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:27:16-59
139            android:value="false" />
139-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:27:60-81
140        <meta-data
140-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:28:5-105
141            android:name="expo.modules.updates.EXPO_UPDATES_CHECK_ON_LAUNCH"
141-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:28:16-80
142            android:value="ALWAYS" />
142-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:28:81-103
143        <meta-data
143-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:29:5-99
144            android:name="expo.modules.updates.EXPO_UPDATES_LAUNCH_WAIT_MS"
144-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:29:16-79
145            android:value="0" />
145-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:29:80-97
146
147        <activity
147-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:30:5-42:16
148            android:name="com.gokul719.snack97152fc1f368437dac54171df4ba22bd.MainActivity"
148-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:30:15-43
149            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|screenLayout|uiMode"
149-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:30:44-134
150            android:exported="true"
150-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:30:256-279
151            android:launchMode="singleTask"
151-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:30:135-166
152            android:screenOrientation="portrait"
152-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:30:280-316
153            android:theme="@style/Theme.App.SplashScreen"
153-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:30:210-255
154            android:windowSoftInputMode="adjustResize" >
154-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:30:167-209
155            <intent-filter>
155-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:31:7-34:23
156                <action android:name="android.intent.action.MAIN" />
156-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:32:9-60
156-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:32:17-58
157
158                <category android:name="android.intent.category.LAUNCHER" />
158-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:33:9-68
158-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:33:19-66
159            </intent-filter>
160            <intent-filter>
160-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:35:7-41:23
161                <action android:name="android.intent.action.VIEW" />
161-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:14:7-58
161-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:14:15-56
162
163                <category android:name="android.intent.category.DEFAULT" />
163-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:37:9-67
163-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:37:19-65
164                <category android:name="android.intent.category.BROWSABLE" />
164-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:15:7-67
164-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:15:17-65
165
166                <data android:scheme="com.gokul719.snack97152fc1f368437dac54171df4ba22bd" />
166-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:16:7-37
166-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:16:13-35
167                <data android:scheme="exp+snack-97152fc1-f368-437d-ac54-171df4ba22bd" />
167-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:16:7-37
167-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:16:13-35
168            </intent-filter>
169        </activity>
170        <!--
171           This may generate a warning during your build:
172
173           > property#android.adservices.AD_SERVICES_CONFIG@android:resource
174           > was tagged at AndroidManifest.xml:23 to replace other declarations
175           > but no other declaration present
176
177           You may safely ignore this warning.
178
179           We must include this in case you also use Firebase Analytics in some
180           of its configurations, as it may also include this file, and the two
181           will collide and cause a build error if we don't set this one to take
182           priority via replacement.
183
184           https://github.com/invertase/react-native-google-mobile-ads/issues/657
185        -->
186        <property
186-->[:react-native-google-mobile-ads] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-google-mobile-ads\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:42:9-45:48
187            android:name="android.adservices.AD_SERVICES_CONFIG"
187-->[:react-native-google-mobile-ads] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-google-mobile-ads\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:43:13-65
188            android:resource="@xml/gma_ad_services_config" />
188-->[:react-native-google-mobile-ads] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-google-mobile-ads\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:44:13-59
189
190        <provider
190-->[:react-native-webview] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:9-16:20
191            android:name="com.reactnativecommunity.webview.RNCWebViewFileProvider"
191-->[:react-native-webview] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:13-83
192            android:authorities="com.gokul719.snack97152fc1f368437dac54171df4ba22bd.fileprovider"
192-->[:react-native-webview] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:10:13-64
193            android:exported="false"
193-->[:react-native-webview] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:13-37
194            android:grantUriPermissions="true" >
194-->[:react-native-webview] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:12:13-47
195            <meta-data
195-->[:react-native-webview] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:13:13-15:63
196                android:name="android.support.FILE_PROVIDER_PATHS"
196-->[:react-native-webview] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:14:17-67
197                android:resource="@xml/file_provider_paths" />
197-->[:react-native-webview] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:15:17-60
198        </provider>
199        <provider
199-->[:expo-clipboard] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-clipboard\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:9-15:20
200            android:name="expo.modules.clipboard.ClipboardFileProvider"
200-->[:expo-clipboard] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-clipboard\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:13-72
201            android:authorities="com.gokul719.snack97152fc1f368437dac54171df4ba22bd.ClipboardFileProvider"
201-->[:expo-clipboard] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-clipboard\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:10:13-73
202            android:exported="true" >
202-->[:expo-clipboard] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-clipboard\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:13-36
203            <meta-data
203-->[:expo-clipboard] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-clipboard\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:12:13-14:68
204                android:name="expo.modules.clipboard.CLIPBOARD_FILE_PROVIDER_PATHS"
204-->[:expo-clipboard] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-clipboard\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:13:17-84
205                android:resource="@xml/clipboard_provider_paths" />
205-->[:expo-clipboard] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-clipboard\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:14:17-65
206        </provider>
207        <provider
207-->[:expo-file-system] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:21:9-30:20
208            android:name="expo.modules.filesystem.FileSystemFileProvider"
208-->[:expo-file-system] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:22:13-74
209            android:authorities="com.gokul719.snack97152fc1f368437dac54171df4ba22bd.FileSystemFileProvider"
209-->[:expo-file-system] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:23:13-74
210            android:exported="false"
210-->[:expo-file-system] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:24:13-37
211            android:grantUriPermissions="true" >
211-->[:expo-file-system] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:25:13-47
212            <meta-data
212-->[:react-native-webview] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:13:13-15:63
213                android:name="android.support.FILE_PROVIDER_PATHS"
213-->[:react-native-webview] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:14:17-67
214                android:resource="@xml/file_system_provider_paths" />
214-->[:react-native-webview] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:15:17-60
215        </provider>
216
217        <service
217-->[:expo-image-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:28:9-40:19
218            android:name="com.google.android.gms.metadata.ModuleDependencies"
218-->[:expo-image-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:29:13-78
219            android:enabled="false"
219-->[:expo-image-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:30:13-36
220            android:exported="false" >
220-->[:expo-image-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:31:13-37
221            <intent-filter>
221-->[:expo-image-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:33:13-35:29
222                <action android:name="com.google.android.gms.metadata.MODULE_DEPENDENCIES" />
222-->[:expo-image-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:34:17-94
222-->[:expo-image-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:34:25-91
223            </intent-filter>
224
225            <meta-data
225-->[:expo-image-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:37:13-39:36
226                android:name="photopicker_activity:0:required"
226-->[:expo-image-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:38:17-63
227                android:value="" />
227-->[:expo-image-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:39:17-33
228        </service>
229
230        <activity
230-->[:expo-image-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:42:9-44:59
231            android:name="com.canhub.cropper.CropImageActivity"
231-->[:expo-image-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:43:13-64
232            android:exported="true"
232-->[com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\084b6a12791f1cb8a95d16811ba661d8\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:35:13-36
233            android:theme="@style/Base.Theme.AppCompat" /> <!-- https://developer.android.com/guide/topics/manifest/provider-element.html -->
233-->[:expo-image-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:44:13-56
234        <provider
234-->[:expo-image-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:46:9-54:20
235            android:name="expo.modules.imagepicker.fileprovider.ImagePickerFileProvider"
235-->[:expo-image-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:47:13-89
236            android:authorities="com.gokul719.snack97152fc1f368437dac54171df4ba22bd.ImagePickerFileProvider"
236-->[:expo-image-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:48:13-75
237            android:exported="false"
237-->[:expo-image-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:49:13-37
238            android:grantUriPermissions="true" >
238-->[:expo-image-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:50:13-47
239            <meta-data
239-->[:react-native-webview] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:13:13-15:63
240                android:name="android.support.FILE_PROVIDER_PATHS"
240-->[:react-native-webview] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:14:17-67
241                android:resource="@xml/image_picker_provider_paths" />
241-->[:react-native-webview] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:15:17-60
242        </provider>
243
244        <service
244-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:9-17:19
245            android:name="expo.modules.notifications.service.ExpoFirebaseMessagingService"
245-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:12:13-91
246            android:exported="false" >
246-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:13:13-37
247            <intent-filter android:priority="-1" >
247-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:14:13-16:29
247-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:14:28-49
248                <action android:name="com.google.firebase.MESSAGING_EVENT" />
248-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:15:17-78
248-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:15:25-75
249            </intent-filter>
250        </service>
251
252        <receiver
252-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:19:9-31:20
253            android:name="expo.modules.notifications.service.NotificationsService"
253-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:20:13-83
254            android:enabled="true"
254-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:21:13-35
255            android:exported="false" >
255-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:22:13-37
256            <intent-filter android:priority="-1" >
256-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:23:13-30:29
256-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:23:28-49
257                <action android:name="expo.modules.notifications.NOTIFICATION_EVENT" />
257-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:24:17-88
257-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:24:25-85
258                <action android:name="android.intent.action.BOOT_COMPLETED" />
258-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:25:17-79
258-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:25:25-76
259                <action android:name="android.intent.action.REBOOT" />
259-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:26:17-71
259-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:26:25-68
260                <action android:name="android.intent.action.QUICKBOOT_POWERON" />
260-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:27:17-82
260-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:27:25-79
261                <action android:name="com.htc.intent.action.QUICKBOOT_POWERON" />
261-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:28:17-82
261-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:28:25-79
262                <action android:name="android.intent.action.MY_PACKAGE_REPLACED" />
262-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:29:17-84
262-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:29:25-81
263            </intent-filter>
264        </receiver>
265
266        <activity
266-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:33:9-40:75
267            android:name="expo.modules.notifications.service.NotificationForwarderActivity"
267-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:34:13-92
268            android:excludeFromRecents="true"
268-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:35:13-46
269            android:exported="false"
269-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:36:13-37
270            android:launchMode="standard"
270-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:37:13-42
271            android:noHistory="true"
271-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:38:13-37
272            android:taskAffinity=""
272-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:39:13-36
273            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
273-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:40:13-72
274
275        <provider
275-->[:expo-sharing] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-sharing\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:18:9-26:20
276            android:name="expo.modules.sharing.SharingFileProvider"
276-->[:expo-sharing] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-sharing\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:19:13-68
277            android:authorities="com.gokul719.snack97152fc1f368437dac54171df4ba22bd.SharingFileProvider"
277-->[:expo-sharing] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-sharing\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:20:13-71
278            android:exported="false"
278-->[:expo-sharing] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-sharing\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:21:13-37
279            android:grantUriPermissions="true" >
279-->[:expo-sharing] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-sharing\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:22:13-47
280            <meta-data
280-->[:react-native-webview] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:13:13-15:63
281                android:name="android.support.FILE_PROVIDER_PATHS"
281-->[:react-native-webview] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:14:17-67
282                android:resource="@xml/sharing_provider_paths" />
282-->[:react-native-webview] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:15:17-60
283        </provider>
284
285        <meta-data
285-->[:expo-modules-core] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:9-11:89
286            android:name="org.unimodules.core.AppLoader#react-native-headless"
286-->[:expo-modules-core] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:10:13-79
287            android:value="expo.modules.adapters.react.apploader.RNHeadlessAppLoader" />
287-->[:expo-modules-core] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:13-86
288        <meta-data
288-->[:expo-modules-core] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:12:9-15:45
289            android:name="com.facebook.soloader.enabled"
289-->[:expo-modules-core] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:13:13-57
290            android:value="true" />
290-->[:expo-modules-core] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:14:13-33
291        <meta-data
291-->[com.github.bumptech.glide:okhttp3-integration:4.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\89b40201870c87f9d6b8ecc165e999d7\transformed\okhttp3-integration-4.12.0\AndroidManifest.xml:11:9-13:43
292            android:name="com.bumptech.glide.integration.okhttp3.OkHttpGlideModule"
292-->[com.github.bumptech.glide:okhttp3-integration:4.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\89b40201870c87f9d6b8ecc165e999d7\transformed\okhttp3-integration-4.12.0\AndroidManifest.xml:12:13-84
293            android:value="GlideModule" />
293-->[com.github.bumptech.glide:okhttp3-integration:4.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\89b40201870c87f9d6b8ecc165e999d7\transformed\okhttp3-integration-4.12.0\AndroidManifest.xml:13:13-40
294
295        <provider
295-->[com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\084b6a12791f1cb8a95d16811ba661d8\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:23:9-31:20
296            android:name="com.canhub.cropper.CropFileProvider"
296-->[com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\084b6a12791f1cb8a95d16811ba661d8\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:24:13-63
297            android:authorities="com.gokul719.snack97152fc1f368437dac54171df4ba22bd.cropper.fileprovider"
297-->[com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\084b6a12791f1cb8a95d16811ba661d8\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:25:13-72
298            android:exported="false"
298-->[com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\084b6a12791f1cb8a95d16811ba661d8\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:26:13-37
299            android:grantUriPermissions="true" >
299-->[com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\084b6a12791f1cb8a95d16811ba661d8\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:27:13-47
300            <meta-data
300-->[:react-native-webview] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:13:13-15:63
301                android:name="android.support.FILE_PROVIDER_PATHS"
301-->[:react-native-webview] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:14:17-67
302                android:resource="@xml/library_file_paths" />
302-->[:react-native-webview] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:15:17-60
303        </provider> <!-- Include the AdActivity and InAppPurchaseActivity configChanges and themes. -->
304        <activity
304-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6541660dec70774a79856ee0aa1798c7\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:56:9-61:43
305            android:name="com.google.android.gms.ads.AdActivity"
305-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6541660dec70774a79856ee0aa1798c7\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:57:13-65
306            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|uiMode|screenSize|smallestScreenSize"
306-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6541660dec70774a79856ee0aa1798c7\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:58:13-122
307            android:exported="false"
307-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6541660dec70774a79856ee0aa1798c7\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:59:13-37
308            android:theme="@android:style/Theme.Translucent" />
308-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6541660dec70774a79856ee0aa1798c7\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:60:13-61
309
310        <provider
310-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6541660dec70774a79856ee0aa1798c7\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:63:9-68:43
311            android:name="com.google.android.gms.ads.MobileAdsInitProvider"
311-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6541660dec70774a79856ee0aa1798c7\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:64:13-76
312            android:authorities="com.gokul719.snack97152fc1f368437dac54171df4ba22bd.mobileadsinitprovider"
312-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6541660dec70774a79856ee0aa1798c7\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:65:13-73
313            android:exported="false"
313-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6541660dec70774a79856ee0aa1798c7\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:66:13-37
314            android:initOrder="100" />
314-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6541660dec70774a79856ee0aa1798c7\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:67:13-36
315
316        <service
316-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6541660dec70774a79856ee0aa1798c7\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:70:9-74:43
317            android:name="com.google.android.gms.ads.AdService"
317-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6541660dec70774a79856ee0aa1798c7\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:71:13-64
318            android:enabled="true"
318-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6541660dec70774a79856ee0aa1798c7\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:72:13-35
319            android:exported="false" />
319-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6541660dec70774a79856ee0aa1798c7\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:73:13-37
320
321        <activity
321-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6541660dec70774a79856ee0aa1798c7\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:76:9-80:43
322            android:name="com.google.android.gms.ads.OutOfContextTestingActivity"
322-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6541660dec70774a79856ee0aa1798c7\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:77:13-82
323            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|uiMode|screenSize|smallestScreenSize"
323-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6541660dec70774a79856ee0aa1798c7\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:78:13-122
324            android:exported="false" />
324-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6541660dec70774a79856ee0aa1798c7\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:79:13-37
325        <activity
325-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6541660dec70774a79856ee0aa1798c7\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:81:9-88:43
326            android:name="com.google.android.gms.ads.NotificationHandlerActivity"
326-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6541660dec70774a79856ee0aa1798c7\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:82:13-82
327            android:excludeFromRecents="true"
327-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6541660dec70774a79856ee0aa1798c7\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:83:13-46
328            android:exported="false"
328-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6541660dec70774a79856ee0aa1798c7\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:84:13-37
329            android:launchMode="singleTask"
329-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6541660dec70774a79856ee0aa1798c7\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:85:13-44
330            android:taskAffinity=""
330-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6541660dec70774a79856ee0aa1798c7\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:86:13-36
331            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
331-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6541660dec70774a79856ee0aa1798c7\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:87:13-72
332
333        <receiver
333-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c48c306fa29cacf340e1042583924ca8\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:29:9-40:20
334            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
334-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c48c306fa29cacf340e1042583924ca8\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:30:13-78
335            android:exported="true"
335-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c48c306fa29cacf340e1042583924ca8\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:31:13-36
336            android:permission="com.google.android.c2dm.permission.SEND" >
336-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c48c306fa29cacf340e1042583924ca8\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:32:13-73
337            <intent-filter>
337-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c48c306fa29cacf340e1042583924ca8\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:33:13-35:29
338                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
338-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c48c306fa29cacf340e1042583924ca8\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:34:17-81
338-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c48c306fa29cacf340e1042583924ca8\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:34:25-78
339            </intent-filter>
340
341            <meta-data
341-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c48c306fa29cacf340e1042583924ca8\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:37:13-39:40
342                android:name="com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED"
342-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c48c306fa29cacf340e1042583924ca8\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:38:17-92
343                android:value="true" />
343-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c48c306fa29cacf340e1042583924ca8\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:39:17-37
344        </receiver>
345        <!--
346             FirebaseMessagingService performs security checks at runtime,
347             but set to not exported to explicitly avoid allowing another app to call it.
348        -->
349        <service
349-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c48c306fa29cacf340e1042583924ca8\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:46:9-53:19
350            android:name="com.google.firebase.messaging.FirebaseMessagingService"
350-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c48c306fa29cacf340e1042583924ca8\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:47:13-82
351            android:directBootAware="true"
351-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c48c306fa29cacf340e1042583924ca8\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:48:13-43
352            android:exported="false" >
352-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c48c306fa29cacf340e1042583924ca8\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:49:13-37
353            <intent-filter android:priority="-500" >
353-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:14:13-16:29
353-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:14:28-49
354                <action android:name="com.google.firebase.MESSAGING_EVENT" />
354-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:15:17-78
354-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:15:25-75
355            </intent-filter>
356        </service>
357        <service
357-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c48c306fa29cacf340e1042583924ca8\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:54:9-63:19
358            android:name="com.google.firebase.components.ComponentDiscoveryService"
358-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c48c306fa29cacf340e1042583924ca8\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:55:13-84
359            android:directBootAware="true"
359-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3af9264cb24c523080e789fe1aeda055\transformed\firebase-common-21.0.0\AndroidManifest.xml:32:13-43
360            android:exported="false" >
360-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c48c306fa29cacf340e1042583924ca8\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:56:13-37
361            <meta-data
361-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c48c306fa29cacf340e1042583924ca8\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:57:13-59:85
362                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar"
362-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c48c306fa29cacf340e1042583924ca8\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:58:17-122
363                android:value="com.google.firebase.components.ComponentRegistrar" />
363-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c48c306fa29cacf340e1042583924ca8\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:59:17-82
364            <meta-data
364-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c48c306fa29cacf340e1042583924ca8\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:60:13-62:85
365                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar"
365-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c48c306fa29cacf340e1042583924ca8\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:61:17-119
366                android:value="com.google.firebase.components.ComponentRegistrar" />
366-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c48c306fa29cacf340e1042583924ca8\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:62:17-82
367            <meta-data
367-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3abe18b93b03aa88f4479351be1225c9\transformed\firebase-installations-17.2.0\AndroidManifest.xml:15:13-17:85
368                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
368-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3abe18b93b03aa88f4479351be1225c9\transformed\firebase-installations-17.2.0\AndroidManifest.xml:16:17-130
369                android:value="com.google.firebase.components.ComponentRegistrar" />
369-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3abe18b93b03aa88f4479351be1225c9\transformed\firebase-installations-17.2.0\AndroidManifest.xml:17:17-82
370            <meta-data
370-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3abe18b93b03aa88f4479351be1225c9\transformed\firebase-installations-17.2.0\AndroidManifest.xml:18:13-20:85
371                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
371-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3abe18b93b03aa88f4479351be1225c9\transformed\firebase-installations-17.2.0\AndroidManifest.xml:19:17-127
372                android:value="com.google.firebase.components.ComponentRegistrar" />
372-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3abe18b93b03aa88f4479351be1225c9\transformed\firebase-installations-17.2.0\AndroidManifest.xml:20:17-82
373            <meta-data
373-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c58bf33df59e38220a67da64c5f2651f\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
374                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
374-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c58bf33df59e38220a67da64c5f2651f\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
375                android:value="com.google.firebase.components.ComponentRegistrar" />
375-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c58bf33df59e38220a67da64c5f2651f\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
376            <meta-data
376-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3af9264cb24c523080e789fe1aeda055\transformed\firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
377                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
377-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3af9264cb24c523080e789fe1aeda055\transformed\firebase-common-21.0.0\AndroidManifest.xml:36:17-109
378                android:value="com.google.firebase.components.ComponentRegistrar" />
378-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3af9264cb24c523080e789fe1aeda055\transformed\firebase-common-21.0.0\AndroidManifest.xml:37:17-82
379            <meta-data
379-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c425ae13f88cc4eff0fa0c7b762ff882\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:25:13-27:85
380                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
380-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c425ae13f88cc4eff0fa0c7b762ff882\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:26:17-115
381                android:value="com.google.firebase.components.ComponentRegistrar" />
381-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c425ae13f88cc4eff0fa0c7b762ff882\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:27:17-82
382        </service>
383
384        <provider
384-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3af9264cb24c523080e789fe1aeda055\transformed\firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
385            android:name="com.google.firebase.provider.FirebaseInitProvider"
385-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3af9264cb24c523080e789fe1aeda055\transformed\firebase-common-21.0.0\AndroidManifest.xml:24:13-77
386            android:authorities="com.gokul719.snack97152fc1f368437dac54171df4ba22bd.firebaseinitprovider"
386-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3af9264cb24c523080e789fe1aeda055\transformed\firebase-common-21.0.0\AndroidManifest.xml:25:13-72
387            android:directBootAware="true"
387-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3af9264cb24c523080e789fe1aeda055\transformed\firebase-common-21.0.0\AndroidManifest.xml:26:13-43
388            android:exported="false"
388-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3af9264cb24c523080e789fe1aeda055\transformed\firebase-common-21.0.0\AndroidManifest.xml:27:13-37
389            android:initOrder="100" />
389-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3af9264cb24c523080e789fe1aeda055\transformed\firebase-common-21.0.0\AndroidManifest.xml:28:13-36
390
391        <activity
391-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\263c8e8847d6991526302086ecc6e182\transformed\play-services-base-18.0.1\AndroidManifest.xml:20:9-22:45
392            android:name="com.google.android.gms.common.api.GoogleApiActivity"
392-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\263c8e8847d6991526302086ecc6e182\transformed\play-services-base-18.0.1\AndroidManifest.xml:20:19-85
393            android:exported="false"
393-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\263c8e8847d6991526302086ecc6e182\transformed\play-services-base-18.0.1\AndroidManifest.xml:22:19-43
394            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
394-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\263c8e8847d6991526302086ecc6e182\transformed\play-services-base-18.0.1\AndroidManifest.xml:21:19-78
395
396        <provider
396-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:31:9-39:20
397            android:name="androidx.startup.InitializationProvider"
397-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:32:13-67
398            android:authorities="com.gokul719.snack97152fc1f368437dac54171df4ba22bd.androidx-startup"
398-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:33:13-68
399            android:exported="false" >
399-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:34:13-37
400            <meta-data
400-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:36:13-38:52
401                android:name="androidx.work.WorkManagerInitializer"
401-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:37:17-68
402                android:value="androidx.startup" />
402-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:38:17-49
403            <meta-data
403-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\270359720fa256563d55dec8f9480bc0\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
404                android:name="androidx.emoji2.text.EmojiCompatInitializer"
404-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\270359720fa256563d55dec8f9480bc0\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
405                android:value="androidx.startup" />
405-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\270359720fa256563d55dec8f9480bc0\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
406            <meta-data
406-->[androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b3059939b2c8092c03a8dedf19cca0e7\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:29:13-31:52
407                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
407-->[androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b3059939b2c8092c03a8dedf19cca0e7\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:30:17-78
408                android:value="androidx.startup" />
408-->[androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b3059939b2c8092c03a8dedf19cca0e7\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:31:17-49
409            <meta-data
409-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b4fc0c5c3b80165703f803a1e31a276f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
410                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
410-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b4fc0c5c3b80165703f803a1e31a276f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
411                android:value="androidx.startup" />
411-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b4fc0c5c3b80165703f803a1e31a276f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
412        </provider>
413
414        <service
414-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:41:9-46:35
415            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
415-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:42:13-88
416            android:directBootAware="false"
416-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:43:13-44
417            android:enabled="@bool/enable_system_alarm_service_default"
417-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:44:13-72
418            android:exported="false" />
418-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:45:13-37
419        <service
419-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:47:9-53:35
420            android:name="androidx.work.impl.background.systemjob.SystemJobService"
420-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:48:13-84
421            android:directBootAware="false"
421-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:49:13-44
422            android:enabled="@bool/enable_system_job_service_default"
422-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:50:13-70
423            android:exported="true"
423-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:51:13-36
424            android:permission="android.permission.BIND_JOB_SERVICE" />
424-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:52:13-69
425        <service
425-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:54:9-59:35
426            android:name="androidx.work.impl.foreground.SystemForegroundService"
426-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:55:13-81
427            android:directBootAware="false"
427-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:56:13-44
428            android:enabled="@bool/enable_system_foreground_service_default"
428-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:57:13-77
429            android:exported="false" />
429-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:58:13-37
430
431        <receiver
431-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:61:9-66:35
432            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
432-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:62:13-88
433            android:directBootAware="false"
433-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:63:13-44
434            android:enabled="true"
434-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:64:13-35
435            android:exported="false" />
435-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:65:13-37
436        <receiver
436-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:67:9-77:20
437            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
437-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:68:13-106
438            android:directBootAware="false"
438-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:69:13-44
439            android:enabled="false"
439-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:70:13-36
440            android:exported="false" >
440-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:71:13-37
441            <intent-filter>
441-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:73:13-76:29
442                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
442-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:74:17-87
442-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:74:25-84
443                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
443-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:75:17-90
443-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:75:25-87
444            </intent-filter>
445        </receiver>
446        <receiver
446-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:78:9-88:20
447            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
447-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:79:13-104
448            android:directBootAware="false"
448-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:80:13-44
449            android:enabled="false"
449-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:81:13-36
450            android:exported="false" >
450-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:82:13-37
451            <intent-filter>
451-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:84:13-87:29
452                <action android:name="android.intent.action.BATTERY_OKAY" />
452-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:85:17-77
452-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:85:25-74
453                <action android:name="android.intent.action.BATTERY_LOW" />
453-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:86:17-76
453-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:86:25-73
454            </intent-filter>
455        </receiver>
456        <receiver
456-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:89:9-99:20
457            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
457-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:90:13-104
458            android:directBootAware="false"
458-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:91:13-44
459            android:enabled="false"
459-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:92:13-36
460            android:exported="false" >
460-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:93:13-37
461            <intent-filter>
461-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:95:13-98:29
462                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
462-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:96:17-83
462-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:96:25-80
463                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
463-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:97:17-82
463-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:97:25-79
464            </intent-filter>
465        </receiver>
466        <receiver
466-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:100:9-109:20
467            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
467-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:101:13-103
468            android:directBootAware="false"
468-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:102:13-44
469            android:enabled="false"
469-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:103:13-36
470            android:exported="false" >
470-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:104:13-37
471            <intent-filter>
471-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:106:13-108:29
472                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
472-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:107:17-79
472-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:107:25-76
473            </intent-filter>
474        </receiver>
475        <receiver
475-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:110:9-121:20
476            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
476-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:111:13-88
477            android:directBootAware="false"
477-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:112:13-44
478            android:enabled="false"
478-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:113:13-36
479            android:exported="false" >
479-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:114:13-37
480            <intent-filter>
480-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:116:13-120:29
481                <action android:name="android.intent.action.BOOT_COMPLETED" />
481-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:25:17-79
481-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:25:25-76
482                <action android:name="android.intent.action.TIME_SET" />
482-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:118:17-73
482-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:118:25-70
483                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
483-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:119:17-81
483-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:119:25-78
484            </intent-filter>
485        </receiver>
486        <receiver
486-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:122:9-131:20
487            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
487-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:123:13-99
488            android:directBootAware="false"
488-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:124:13-44
489            android:enabled="@bool/enable_system_alarm_service_default"
489-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:125:13-72
490            android:exported="false" >
490-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:126:13-37
491            <intent-filter>
491-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:128:13-130:29
492                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
492-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:129:17-98
492-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:129:25-95
493            </intent-filter>
494        </receiver>
495        <receiver
495-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:132:9-142:20
496            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
496-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:133:13-78
497            android:directBootAware="false"
497-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:134:13-44
498            android:enabled="true"
498-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:135:13-35
499            android:exported="true"
499-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:136:13-36
500            android:permission="android.permission.DUMP" >
500-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:137:13-57
501            <intent-filter>
501-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:139:13-141:29
502                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
502-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:140:17-88
502-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fd2f0b11137ae0d09b853a4946692f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:140:25-85
503            </intent-filter>
504        </receiver>
505
506        <uses-library
506-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\701c966945a05b6cfc9f2e519b7eeb8e\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:23:9-25:40
507            android:name="android.ext.adservices"
507-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\701c966945a05b6cfc9f2e519b7eeb8e\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:24:13-50
508            android:required="false" />
508-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\701c966945a05b6cfc9f2e519b7eeb8e\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:25:13-37
509
510        <meta-data
510-->[com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\02b86cfeeba2fc323c383047854b9491\transformed\play-services-basement-18.2.0\AndroidManifest.xml:21:9-23:69
511            android:name="com.google.android.gms.version"
511-->[com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\02b86cfeeba2fc323c383047854b9491\transformed\play-services-basement-18.2.0\AndroidManifest.xml:22:13-58
512            android:value="@integer/google_play_services_version" />
512-->[com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\02b86cfeeba2fc323c383047854b9491\transformed\play-services-basement-18.2.0\AndroidManifest.xml:23:13-66
513
514        <receiver
514-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b4fc0c5c3b80165703f803a1e31a276f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
515            android:name="androidx.profileinstaller.ProfileInstallReceiver"
515-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b4fc0c5c3b80165703f803a1e31a276f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
516            android:directBootAware="false"
516-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b4fc0c5c3b80165703f803a1e31a276f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
517            android:enabled="true"
517-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b4fc0c5c3b80165703f803a1e31a276f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
518            android:exported="true"
518-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b4fc0c5c3b80165703f803a1e31a276f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
519            android:permission="android.permission.DUMP" >
519-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b4fc0c5c3b80165703f803a1e31a276f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
520            <intent-filter>
520-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b4fc0c5c3b80165703f803a1e31a276f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
521                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
521-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b4fc0c5c3b80165703f803a1e31a276f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
521-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b4fc0c5c3b80165703f803a1e31a276f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
522            </intent-filter>
523            <intent-filter>
523-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b4fc0c5c3b80165703f803a1e31a276f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
524                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
524-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b4fc0c5c3b80165703f803a1e31a276f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
524-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b4fc0c5c3b80165703f803a1e31a276f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
525            </intent-filter>
526            <intent-filter>
526-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b4fc0c5c3b80165703f803a1e31a276f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
527                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
527-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b4fc0c5c3b80165703f803a1e31a276f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
527-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b4fc0c5c3b80165703f803a1e31a276f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
528            </intent-filter>
529            <intent-filter>
529-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b4fc0c5c3b80165703f803a1e31a276f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
530                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
530-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b4fc0c5c3b80165703f803a1e31a276f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
530-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b4fc0c5c3b80165703f803a1e31a276f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
531            </intent-filter>
532        </receiver>
533
534        <service
534-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\37f7f33bfd0c5f48616a4338c6ce336d\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:28:9-34:19
535            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
535-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\37f7f33bfd0c5f48616a4338c6ce336d\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:29:13-103
536            android:exported="false" >
536-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\37f7f33bfd0c5f48616a4338c6ce336d\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:30:13-37
537            <meta-data
537-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\37f7f33bfd0c5f48616a4338c6ce336d\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:31:13-33:39
538                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
538-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\37f7f33bfd0c5f48616a4338c6ce336d\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:32:17-94
539                android:value="cct" />
539-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\37f7f33bfd0c5f48616a4338c6ce336d\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:33:17-36
540        </service>
541        <service
541-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8141aeabd6dd325d2cf83449b27069b5\transformed\transport-runtime-3.1.9\AndroidManifest.xml:26:9-30:19
542            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
542-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8141aeabd6dd325d2cf83449b27069b5\transformed\transport-runtime-3.1.9\AndroidManifest.xml:27:13-117
543            android:exported="false"
543-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8141aeabd6dd325d2cf83449b27069b5\transformed\transport-runtime-3.1.9\AndroidManifest.xml:28:13-37
544            android:permission="android.permission.BIND_JOB_SERVICE" >
544-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8141aeabd6dd325d2cf83449b27069b5\transformed\transport-runtime-3.1.9\AndroidManifest.xml:29:13-69
545        </service>
546
547        <receiver
547-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8141aeabd6dd325d2cf83449b27069b5\transformed\transport-runtime-3.1.9\AndroidManifest.xml:32:9-34:40
548            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
548-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8141aeabd6dd325d2cf83449b27069b5\transformed\transport-runtime-3.1.9\AndroidManifest.xml:33:13-132
549            android:exported="false" />
549-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8141aeabd6dd325d2cf83449b27069b5\transformed\transport-runtime-3.1.9\AndroidManifest.xml:34:13-37
550
551        <service
551-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\178194cdae2c08446775c57801d0086b\transformed\room-runtime-2.2.5\AndroidManifest.xml:25:9-28:40
552            android:name="androidx.room.MultiInstanceInvalidationService"
552-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\178194cdae2c08446775c57801d0086b\transformed\room-runtime-2.2.5\AndroidManifest.xml:26:13-74
553            android:directBootAware="true"
553-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\178194cdae2c08446775c57801d0086b\transformed\room-runtime-2.2.5\AndroidManifest.xml:27:13-43
554            android:exported="false" />
554-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\178194cdae2c08446775c57801d0086b\transformed\room-runtime-2.2.5\AndroidManifest.xml:28:13-37
555    </application>
556
557</manifest>
