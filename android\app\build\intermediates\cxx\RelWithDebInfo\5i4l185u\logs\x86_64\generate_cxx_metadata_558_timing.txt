# C/C++ build system timings
generate_cxx_metadata
  [gap of 160ms]
  create-invalidation-state 7922ms
  generate-prefab-packages
    [gap of 271ms]
    exec-prefab 3677ms
    [gap of 160ms]
  generate-prefab-packages completed in 4108ms
  execute-generate-process
    exec-configure 2368ms
    [gap of 494ms]
  execute-generate-process completed in 2867ms
  [gap of 248ms]
  write-metadata-json-to-file 46ms
generate_cxx_metadata completed in 15381ms

